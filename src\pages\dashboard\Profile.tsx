import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
    Edit,
    Upload,
    MapPin,
    Mail,
    Phone,
    Calendar,
    Save,
    Loader2,
    Camera,
} from "lucide-react";
import { useAuth } from "@/contexts/auth/AuthContext";
import {
    useUserProfile,
    useUpdateUserProfile,
    useUploadAvatar,
    useUserStats,
} from "@/hooks/api/useUser";

const Profile = () => {
    const { appUser, isPremium } = useAuth();
    const { data: userProfile, isLoading: isLoadingProfile } = useUserProfile();
    const { data: userStats, isLoading: isLoadingStats } = useUserStats();
    const updateProfileMutation = useUpdateUserProfile();
    const uploadAvatarMutation = useUploadAvatar();

    const [isEditing, setIsEditing] = useState(false);
    const [formData, setFormData] = useState({
        firstName: appUser?.firstName || "",
        lastName: appUser?.lastName || "",
        email: appUser?.email || "",
        phone: appUser?.phone || "",
        location: {
            city: appUser?.location?.city || "",
            state: appUser?.location?.state || "",
            country: appUser?.location?.country || "",
        },
    });

    const handleInputChange = (field: string, value: string) => {
        if (field.includes(".")) {
            const [parent, child] = field.split(".");
            setFormData((prev) => ({
                ...prev,
                [parent]: {
                    ...prev[parent as keyof typeof prev],
                    [child]: value,
                },
            }));
        } else {
            setFormData((prev) => ({
                ...prev,
                [field]: value,
            }));
        }
    };

    const handleSaveProfile = async () => {
        try {
            await updateProfileMutation.mutateAsync(formData);
            setIsEditing(false);
        } catch (error) {
            console.error("Failed to update profile:", error);
        }
    };

    const handleAvatarUpload = async (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        const file = event.target.files?.[0];
        if (file) {
            try {
                await uploadAvatarMutation.mutateAsync(file);
            } catch (error) {
                console.error("Failed to upload avatar:", error);
            }
        }
    };

    if (isLoadingProfile) {
        return (
            <div className="flex items-center justify-center h-64">
                <Loader2 className="h-8 w-8 animate-spin" />
            </div>
        );
    }

    const user = userProfile || appUser;

    return (
        <div className="container mx-auto p-6 max-w-4xl">
            <div className="mb-6">
                <h1 className="text-3xl font-bold">Profile</h1>
                <p className="text-muted-foreground">
                    Manage your account settings and preferences
                </p>
            </div>

            <Tabs defaultValue="profile" className="space-y-6">
                <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="profile">Profile</TabsTrigger>
                    <TabsTrigger value="stats">Statistics</TabsTrigger>
                    <TabsTrigger value="subscription">Subscription</TabsTrigger>
                </TabsList>

                <TabsContent value="profile" className="space-y-6">
                    {/* Profile Header */}
                    <Card>
                        <CardHeader>
                            <div className="flex items-center space-x-4">
                                <div className="relative">
                                    <Avatar className="h-20 w-20">
                                        <AvatarImage
                                            src={user?.avatar}
                                            alt={`${user?.firstName} ${user?.lastName}`}
                                        />
                                        <AvatarFallback className="text-lg">
                                            {user?.firstName?.[0]}
                                            {user?.lastName?.[0]}
                                        </AvatarFallback>
                                    </Avatar>
                                    <label
                                        htmlFor="avatar-upload"
                                        className="absolute bottom-0 right-0 p-1 bg-primary rounded-full cursor-pointer hover:bg-primary/90"
                                    >
                                        <Camera className="h-3 w-3 text-primary-foreground" />
                                        <input
                                            id="avatar-upload"
                                            type="file"
                                            accept="image/*"
                                            className="hidden"
                                            onChange={handleAvatarUpload}
                                            disabled={
                                                uploadAvatarMutation.isPending
                                            }
                                        />
                                    </label>
                                </div>
                                <div className="flex-1">
                                    <h2 className="text-2xl font-bold">
                                        {user?.firstName} {user?.lastName}
                                    </h2>
                                    <p className="text-muted-foreground">
                                        {user?.email}
                                    </p>
                                    <div className="flex items-center space-x-2 mt-2">
                                        <Badge
                                            variant={
                                                isPremium
                                                    ? "default"
                                                    : "secondary"
                                            }
                                        >
                                            {user?.subscription?.plan || "FREE"}
                                        </Badge>
                                        {isPremium && (
                                            <Badge variant="outline">
                                                Premium Member
                                            </Badge>
                                        )}
                                    </div>
                                </div>
                                <div className="flex space-x-2">
                                    {isEditing ? (
                                        <>
                                            <Button
                                                onClick={handleSaveProfile}
                                                disabled={
                                                    updateProfileMutation.isPending
                                                }
                                            >
                                                {updateProfileMutation.isPending ? (
                                                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                                ) : (
                                                    <Save className="h-4 w-4 mr-2" />
                                                )}
                                                Save
                                            </Button>
                                            <Button
                                                variant="outline"
                                                onClick={() =>
                                                    setIsEditing(false)
                                                }
                                            >
                                                Cancel
                                            </Button>
                                        </>
                                    ) : (
                                        <Button
                                            onClick={() => setIsEditing(true)}
                                        >
                                            <Edit className="h-4 w-4 mr-2" />
                                            Edit Profile
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </CardHeader>
                    </Card>

                    {/* Basic Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Basic Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="firstName">
                                        First Name
                                    </Label>
                                    <Input
                                        id="firstName"
                                        value={
                                            isEditing
                                                ? formData.firstName
                                                : user?.firstName || ""
                                        }
                                        onChange={(e) =>
                                            handleInputChange(
                                                "firstName",
                                                e.target.value
                                            )
                                        }
                                        disabled={!isEditing}
                                        className="mt-1"
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="lastName">Last Name</Label>
                                    <Input
                                        id="lastName"
                                        value={
                                            isEditing
                                                ? formData.lastName
                                                : user?.lastName || ""
                                        }
                                        onChange={(e) =>
                                            handleInputChange(
                                                "lastName",
                                                e.target.value
                                            )
                                        }
                                        disabled={!isEditing}
                                        className="mt-1"
                                    />
                                </div>
                            </div>

                            <div>
                                <Label htmlFor="email">Email Address</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={
                                        isEditing
                                            ? formData.email
                                            : user?.email || ""
                                    }
                                    onChange={(e) =>
                                        handleInputChange(
                                            "email",
                                            e.target.value
                                        )
                                    }
                                    disabled={!isEditing}
                                    className="mt-1"
                                />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="phone">Phone Number</Label>
                                    <Input
                                        id="phone"
                                        type="tel"
                                        value={
                                            isEditing
                                                ? formData.phone
                                                : user?.phone || ""
                                        }
                                        onChange={(e) =>
                                            handleInputChange(
                                                "phone",
                                                e.target.value
                                            )
                                        }
                                        disabled={!isEditing}
                                        placeholder="+****************"
                                        className="mt-1"
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="location">Location</Label>
                                    <Input
                                        id="location"
                                        value={
                                            isEditing
                                                ? `${formData.location.city}, ${formData.location.state}`
                                                : `${
                                                      user?.location?.city || ""
                                                  }, ${
                                                      user?.location?.state ||
                                                      ""
                                                  }`
                                        }
                                        onChange={(e) => {
                                            const [city, state] =
                                                e.target.value.split(", ");
                                            handleInputChange(
                                                "location.city",
                                                city || ""
                                            );
                                            handleInputChange(
                                                "location.state",
                                                state || ""
                                            );
                                        }}
                                        disabled={!isEditing}
                                        placeholder="City, State"
                                        className="mt-1"
                                    />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="stats" className="space-y-6">
                    {isLoadingStats ? (
                        <div className="flex items-center justify-center h-64">
                            <Loader2 className="h-8 w-8 animate-spin" />
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center space-x-2">
                                        <div className="p-2 bg-blue-100 rounded-lg">
                                            <Mail className="h-4 w-4 text-blue-600" />
                                        </div>
                                        <div>
                                            <p className="text-sm text-muted-foreground">
                                                Total Resumes
                                            </p>
                                            <p className="text-2xl font-bold">
                                                {userStats?.totalResumes || 0}
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center space-x-2">
                                        <div className="p-2 bg-green-100 rounded-lg">
                                            <Phone className="h-4 w-4 text-green-600" />
                                        </div>
                                        <div>
                                            <p className="text-sm text-muted-foreground">
                                                Applications
                                            </p>
                                            <p className="text-2xl font-bold">
                                                {userStats?.totalApplications ||
                                                    0}
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center space-x-2">
                                        <div className="p-2 bg-purple-100 rounded-lg">
                                            <Calendar className="h-4 w-4 text-purple-600" />
                                        </div>
                                        <div>
                                            <p className="text-sm text-muted-foreground">
                                                Interviews
                                            </p>
                                            <p className="text-2xl font-bold">
                                                {userStats?.totalInterviews ||
                                                    0}
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardContent className="p-6">
                                    <div className="flex items-center space-x-2">
                                        <div className="p-2 bg-orange-100 rounded-lg">
                                            <MapPin className="h-4 w-4 text-orange-600" />
                                        </div>
                                        <div>
                                            <p className="text-sm text-muted-foreground">
                                                Response Rate
                                            </p>
                                            <p className="text-2xl font-bold">
                                                {userStats?.responseRate || 0}%
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    )}
                </TabsContent>

                <TabsContent value="subscription" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Subscription Details</CardTitle>
                            <CardDescription>
                                Manage your subscription and billing
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="font-semibold">
                                        Current Plan
                                    </h3>
                                    <p className="text-sm text-muted-foreground">
                                        {user?.subscription?.plan || "FREE"}{" "}
                                        Plan
                                    </p>
                                </div>
                                <Badge
                                    variant={
                                        isPremium ? "default" : "secondary"
                                    }
                                >
                                    {user?.subscription?.status || "ACTIVE"}
                                </Badge>
                            </div>

                            {user?.subscription?.usage && (
                                <div className="space-y-3">
                                    <h4 className="font-medium">Usage</h4>
                                    <div className="space-y-2">
                                        <div className="flex justify-between text-sm">
                                            <span>Resume Analyses</span>
                                            <span>
                                                {
                                                    user.subscription.usage
                                                        .resumeAnalyses
                                                }{" "}
                                                /{" "}
                                                {
                                                    user.subscription.usage
                                                        .maxResumeAnalyses
                                                }
                                            </span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span>Job Applications</span>
                                            <span>
                                                {
                                                    user.subscription.usage
                                                        .jobApplications
                                                }{" "}
                                                /{" "}
                                                {
                                                    user.subscription.usage
                                                        .maxJobApplications
                                                }
                                            </span>
                                        </div>
                                        <div className="flex justify-between text-sm">
                                            <span>Interview Sessions</span>
                                            <span>
                                                {
                                                    user.subscription.usage
                                                        .interviewSessions
                                                }{" "}
                                                /{" "}
                                                {
                                                    user.subscription.usage
                                                        .maxInterviewSessions
                                                }
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {!isPremium && (
                                <div className="pt-4">
                                    <Button className="w-full">
                                        Upgrade to Premium
                                    </Button>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default Profile;
