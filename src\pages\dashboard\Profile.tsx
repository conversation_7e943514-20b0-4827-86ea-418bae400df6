import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Edit, Upload, MapPin, Mail, Phone, Calendar } from "lucide-react";
import { useUser } from "@clerk/clerk-react";

const Profile = () => {
    const { user } = useUser();

    return (
        <div className="space-y-6">
            <div>
                <h1 className="text-3xl font-jakarta font-bold text-text-primary">
                    Profile
                </h1>
                <p className="text-text-secondary mt-2">
                    Manage your personal information and professional details
                </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Profile Summary */}
                <div className="lg:col-span-1">
                    <Card>
                        <CardContent className="p-6 text-center">
                            <div className="relative inline-block mb-4">
                                <Avatar className="w-24 h-24">
                                    <AvatarImage
                                        src={user?.profilePicture || ""}
                                    />
                                    <AvatarFallback className="bg-primary text-white text-2xl">
                                        {user?.fullName?.charAt(0)}
                                    </AvatarFallback>
                                </Avatar>
                                <Button
                                    size="sm"
                                    className="absolute -bottom-2 -right-2 rounded-full w-8 h-8 p-0"
                                >
                                    <Upload className="w-4 h-4" />
                                </Button>
                            </div>

                            <h3 className="text-xl font-jakarta font-bold text-text-primary mb-1">
                                {user?.fullName}
                            </h3>
                            <p className="text-text-secondary mb-4">
                                {user?.jobTitle || "Software Engineer"}
                            </p>

                            <div className="space-y-2 text-sm text-text-secondary">
                                <div className="flex items-center justify-center space-x-2">
                                    <Mail className="w-4 h-4" />
                                    <span>
                                        {user?.email || "<EMAIL>"}
                                    </span>
                                </div>
                                <div className="flex items-center justify-center space-x-2">
                                    <MapPin className="w-4 h-4" />
                                    <span>San Francisco, CA</span>
                                </div>
                                <div className="flex items-center justify-center space-x-2">
                                    <Calendar className="w-4 h-4" />
                                    <span>Joined March 2024</span>
                                </div>
                            </div>

                            <Button className="w-full mt-4 bg-primary hover:bg-primary-hover text-white">
                                <Edit className="w-4 h-4 mr-2" />
                                Edit Profile
                            </Button>
                        </CardContent>
                    </Card>
                </div>

                {/* Profile Details */}
                <div className="lg:col-span-2 space-y-6">
                    {/* Basic Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg font-jakarta">
                                Basic Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="firstName">
                                        First Name
                                    </Label>
                                    <Input
                                        id="firstName"
                                        defaultValue={
                                            user?.fullName?.split(" ")[0] || ""
                                        }
                                        className="mt-1"
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="lastName">Last Name</Label>
                                    <Input
                                        id="lastName"
                                        defaultValue={
                                            user?.fullName?.split(" ")[1] || ""
                                        }
                                        className="mt-1"
                                    />
                                </div>
                            </div>

                            <div>
                                <Label htmlFor="email">Email Address</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    defaultValue={user?.email || ""}
                                    className="mt-1"
                                />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="phone">Phone Number</Label>
                                    <Input
                                        id="phone"
                                        type="tel"
                                        placeholder="+****************"
                                        className="mt-1"
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="location">Location</Label>
                                    <Input
                                        id="location"
                                        placeholder="City, State"
                                        className="mt-1"
                                    />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Professional Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg font-jakarta">
                                Professional Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label htmlFor="jobTitle">
                                    Current Job Title
                                </Label>
                                <Input
                                    id="jobTitle"
                                    defaultValue={user?.jobTitle || ""}
                                    className="mt-1"
                                />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="industry">Industry</Label>
                                    <Input
                                        id="industry"
                                        defaultValue={user?.industry || ""}
                                        className="mt-1"
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="experience">
                                        Experience Level
                                    </Label>
                                    <Input
                                        id="experience"
                                        defaultValue={
                                            user?.experienceLevel || ""
                                        }
                                        className="mt-1"
                                    />
                                </div>
                            </div>

                            <div>
                                <Label htmlFor="summary">
                                    Professional Summary
                                </Label>
                                <Textarea
                                    id="summary"
                                    placeholder="Write a brief description of your professional background and career goals..."
                                    rows={4}
                                    className="mt-1"
                                />
                            </div>

                            <div>
                                <Label htmlFor="skills">Skills</Label>
                                <Input
                                    id="skills"
                                    placeholder="React, Python, AWS, etc. (comma separated)"
                                    className="mt-1"
                                />
                            </div>
                        </CardContent>
                    </Card>

                    {/* Save Button */}
                    <div className="flex justify-end">
                        <Button className="bg-primary hover:bg-primary-hover text-white">
                            Save Changes
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Profile;
