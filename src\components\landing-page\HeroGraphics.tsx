
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Check<PERSON>ircle, Zap, Target, TrendingUp } from "lucide-react";

const HeroGraphics = () => {
  return (
    <div className="relative">
      {/* Main illustration area */}
      <div className="relative w-full h-96 bg-gradient-to-br from-brand-blue-50/50 via-purple-50/30 to-pink-50/20 rounded-3xl overflow-hidden">
        {/* Floating elements */}
        <div className="absolute inset-0">
          {/* Resume mockup */}
          <div className="absolute top-12 left-8 w-48 h-64 bg-white rounded-lg shadow-xl transform rotate-3 hover:rotate-6 transition-transform duration-500 animate-float">
            <div className="p-4 space-y-3">
              <div className="w-full h-3 bg-gradient-to-r from-brand-blue-500 to-purple-500 rounded"></div>
              <div className="w-3/4 h-2 bg-gray-200 rounded"></div>
              <div className="w-1/2 h-2 bg-gray-200 rounded"></div>
              <div className="space-y-2 mt-4">
                <div className="w-full h-1.5 bg-gray-100 rounded"></div>
                <div className="w-5/6 h-1.5 bg-gray-100 rounded"></div>
                <div className="w-4/5 h-1.5 bg-gray-100 rounded"></div>
              </div>
            </div>
          </div>

          {/* AI enhancement indicator */}
          <div className="absolute top-20 right-16 bg-white/90 backdrop-blur-sm rounded-full p-3 shadow-lg animate-pulse">
            <Zap className="w-6 h-6 text-yellow-500" />
          </div>

          {/* Success metrics floating cards */}
          <div className="absolute bottom-16 right-8 bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg transform -rotate-2 hover:rotate-0 transition-transform duration-300">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm font-semibold text-gray-900">Match Found!</span>
            </div>
            <div className="text-xs text-gray-600">95% compatibility</div>
            <div className="text-xs text-brand-blue-500 font-medium">$120k • Remote</div>
          </div>

          {/* Target indicator */}
          <div className="absolute bottom-8 left-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full p-3 shadow-lg animate-bounce">
            <Target className="w-5 h-5 text-white" />
          </div>

          {/* Trending arrow */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-purple-500 rounded-full p-4 shadow-xl animate-spin-slow">
            <TrendingUp className="w-8 h-8 text-white" />
          </div>

          {/* Background decoration circles */}
          <div className="absolute top-4 right-4 w-24 h-24 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute bottom-4 left-4 w-32 h-32 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000"></div>
        </div>

        {/* Connecting lines */}
        <svg className="absolute inset-0 w-full h-full pointer-events-none">
          <defs>
            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.6" />
              <stop offset="100%" stopColor="#8B5CF6" stopOpacity="0.3" />
            </linearGradient>
          </defs>
          <path
            d="M100 150 Q200 100 300 180"
            stroke="url(#lineGradient)"
            strokeWidth="2"
            fill="none"
            strokeDasharray="5,5"
            className="animate-pulse"
          />
          <path
            d="M200 250 Q300 200 400 280"
            stroke="url(#lineGradient)"
            strokeWidth="2"
            fill="none"
            strokeDasharray="5,5"
            className="animate-pulse delay-500"
          />
        </svg>
      </div>

      {/* Interactive badges below */}
      <div className="flex justify-center gap-4 mt-8">
        {[
          { icon: CheckCircle, text: "AI-Powered", color: "bg-green-50 text-green-700 border-green-200" },
          { icon: Zap, text: "Instant Results", color: "bg-yellow-50 text-yellow-700 border-yellow-200" },
          { icon: Target, text: "95% Accuracy", color: "bg-blue-50 text-blue-700 border-blue-200" }
        ].map((badge, index) => (
          <Badge
            key={index}
            variant="outline"
            className={`${badge.color} hover:scale-105 transition-transform cursor-pointer px-4 py-2`}
          >
            <badge.icon size={14} className="mr-1" />
            {badge.text}
          </Badge>
        ))}
      </div>
    </div>
  );
};

export default HeroGraphics;
