
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { MessageCircle, X, Star, Users, Clock } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const FloatingActionButton = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsVisible(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {isOpen && (
        <Card className="mb-4 w-80 shadow-xl animate-fade-in">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900">Need Help?</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="h-6 w-6 p-0"
              >
                <X size={14} />
              </Button>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                <div className="w-8 h-8 bg-brand-blue-100 rounded-full flex items-center justify-center">
                  <MessageCircle size={16} className="text-brand-blue-500" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">Live Chat</p>
                  <p className="text-xs text-gray-500">Average response: 2 min</p>
                </div>
                <Badge className="bg-brand-green-100 text-brand-green-700 text-xs">Online</Badge>
              </div>
              
              <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Star size={16} className="text-yellow-500" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">Book Demo</p>
                  <p className="text-xs text-gray-500">Schedule 15-min call</p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <Users size={16} className="text-purple-500" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">Join Community</p>
                  <p className="text-xs text-gray-500">5,000+ members</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      <Button
        onClick={() => setIsOpen(!isOpen)}
        className="w-14 h-14 rounded-full bg-brand-blue-500 hover:bg-brand-blue-600 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
        size="lg"
      >
        {isOpen ? <X size={20} /> : <MessageCircle size={20} />}
      </Button>
    </div>
  );
};

export default FloatingActionButton;
