
import React from 'react';
import { Di<PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { AspectRatio } from "@/components/ui/aspect-ratio";

interface VideoModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const VideoModal = ({ isOpen, onClose }: VideoModalProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>CareerAlgo Demo</DialogTitle>
        </DialogHeader>
        <AspectRatio ratio={16 / 9}>
          <div className="w-full h-full bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="text-4xl mb-4">🎬</div>
              <p className="text-gray-600">Demo video would be embedded here</p>
              <p className="text-sm text-gray-500 mt-2">
                See how <PERSON><PERSON><PERSON><PERSON> transforms your career in 2 minutes
              </p>
            </div>
          </div>
        </AspectRatio>
      </DialogContent>
    </Dialog>
  );
};

export default VideoModal;
