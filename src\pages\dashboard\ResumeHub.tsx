import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    FileText,
    Plus,
    Search,
    Filter,
    Download,
    Edit,
    Copy,
    Trash2,
    MoreVertical,
    Star,
    Calendar,
    Eye,
    Upload,
    Loader2,
} from "lucide-react";
import {
    useResumes,
    useCreateResume,
    useDeleteResume,
    useSetPrimaryResume,
    useDownloadResume,
    useDuplicateResume,
} from "@/hooks/api/useResumes";
import { toast } from "sonner";

interface Resume {
    id: string;
    name: string;
    template: string;
    status: "draft" | "complete" | "published";
    score: number;
    lastModified: string;
    downloads: number;
    isFavorite: boolean;
}

const ResumeHub = () => {
    const [searchTerm, setSearchTerm] = useState("");
    const [filterStatus, setFilterStatus] = useState<
        "all" | "draft" | "complete" | "published"
    >("all");
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [newResumeTitle, setNewResumeTitle] = useState("");
    const [selectedFile, setSelectedFile] = useState<File | null>(null);

    // API hooks
    const { data: resumesData, isLoading, error } = useResumes();
    const createResumeMutation = useCreateResume();
    const deleteResumeMutation = useDeleteResume();
    const setPrimaryMutation = useSetPrimaryResume();
    const downloadMutation = useDownloadResume();
    const duplicateMutation = useDuplicateResume();

    // Use API data or fallback to mock data for demo
    const mockResumes: Resume[] = [
        {
            id: "1",
            name: "Software Engineer Resume",
            template: "Modern",
            status: "complete",
            score: 87,
            lastModified: "2024-01-15",
            downloads: 12,
            isFavorite: true,
        },
        {
            id: "2",
            name: "Frontend Developer CV",
            template: "Creative",
            status: "draft",
            score: 65,
            lastModified: "2024-01-10",
            downloads: 5,
            isFavorite: false,
        },
        {
            id: "3",
            name: "Product Manager Resume",
            template: "Executive",
            status: "published",
            score: 92,
            lastModified: "2024-01-05",
            downloads: 28,
            isFavorite: true,
        },
    ];

    // Use API data if available, otherwise use mock data
    const resumes =
        resumesData?.data?.map((resume) => ({
            id: resume.id,
            name: resume.title,
            template: "Modern", // Default template
            status:
                resume.status === "COMPLETED"
                    ? "complete"
                    : ("draft" as "draft" | "complete" | "published"),
            score: resume.analysis?.overallScore || 0,
            lastModified: resume.updatedAt,
            downloads: 0, // Not available in API
            isFavorite: resume.isPrimary,
        })) || mockResumes;

    const filteredResumes = resumes.filter((resume) => {
        const matchesSearch = resume.name
            .toLowerCase()
            .includes(searchTerm.toLowerCase());
        const matchesFilter =
            filterStatus === "all" || resume.status === filterStatus;
        return matchesSearch && matchesFilter;
    });

    const handleCreateResume = async () => {
        if (!newResumeTitle.trim() || !selectedFile) {
            toast.error("Please provide a title and select a file");
            return;
        }

        try {
            await createResumeMutation.mutateAsync({
                title: newResumeTitle,
                file: selectedFile,
            });
            setIsCreateDialogOpen(false);
            setNewResumeTitle("");
            setSelectedFile(null);
        } catch (error) {
            console.error("Failed to create resume:", error);
        }
    };

    const handleDelete = async (id: string) => {
        try {
            await deleteResumeMutation.mutateAsync(id);
        } catch (error) {
            console.error("Failed to delete resume:", error);
        }
    };

    const handleDuplicate = async (resume: Resume) => {
        try {
            await duplicateMutation.mutateAsync({
                id: resume.id,
                title: `${resume.name} (Copy)`,
            });
        } catch (error) {
            console.error("Failed to duplicate resume:", error);
        }
    };

    const handleDownload = async (id: string) => {
        try {
            await downloadMutation.mutateAsync({
                id,
                options: { format: "PDF" },
            });
        } catch (error) {
            console.error("Failed to download resume:", error);
        }
    };

    const toggleFavorite = async (id: string) => {
        try {
            await setPrimaryMutation.mutateAsync(id);
        } catch (error) {
            console.error("Failed to set primary resume:", error);
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case "complete":
                return "bg-green-100 text-green-800";
            case "draft":
                return "bg-yellow-100 text-yellow-800";
            case "published":
                return "bg-blue-100 text-blue-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    const getScoreColor = (score: number) => {
        if (score >= 80) return "text-green-600";
        if (score >= 60) return "text-yellow-600";
        return "text-red-600";
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-64">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading resumes...</span>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">
                        Resume Hub
                    </h1>
                    <p className="text-gray-600 mt-1">
                        Create, manage, and optimize your resumes
                    </p>
                </div>
                <Dialog
                    open={isCreateDialogOpen}
                    onOpenChange={setIsCreateDialogOpen}
                >
                    <DialogTrigger asChild>
                        <Button className="flex items-center">
                            <Plus className="w-4 h-4 mr-2" />
                            Create New Resume
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Create New Resume</DialogTitle>
                            <DialogDescription>
                                Upload a resume file and give it a title to get
                                started.
                            </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="title">Resume Title</Label>
                                <Input
                                    id="title"
                                    value={newResumeTitle}
                                    onChange={(e) =>
                                        setNewResumeTitle(e.target.value)
                                    }
                                    placeholder="e.g., Software Engineer Resume"
                                />
                            </div>
                            <div>
                                <Label htmlFor="file">Resume File</Label>
                                <Input
                                    id="file"
                                    type="file"
                                    accept=".pdf,.doc,.docx"
                                    onChange={(e) =>
                                        setSelectedFile(
                                            e.target.files?.[0] || null
                                        )
                                    }
                                />
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button
                                    variant="outline"
                                    onClick={() => setIsCreateDialogOpen(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    onClick={handleCreateResume}
                                    disabled={createResumeMutation.isPending}
                                >
                                    {createResumeMutation.isPending ? (
                                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                    ) : (
                                        <Upload className="h-4 w-4 mr-2" />
                                    )}
                                    Create Resume
                                </Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            </div>

            {/* Search and Filter */}
            <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <Input
                        placeholder="Search resumes..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                    />
                </div>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="outline" className="flex items-center">
                            <Filter className="w-4 h-4 mr-2" />
                            Filter:{" "}
                            {filterStatus === "all"
                                ? "All"
                                : filterStatus.charAt(0).toUpperCase() +
                                  filterStatus.slice(1)}
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                        <DropdownMenuItem
                            onClick={() => setFilterStatus("all")}
                        >
                            All Resumes
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            onClick={() => setFilterStatus("draft")}
                        >
                            Draft
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            onClick={() => setFilterStatus("complete")}
                        >
                            Complete
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            onClick={() => setFilterStatus("published")}
                        >
                            Published
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>

            {/* Resume Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredResumes.map((resume) => (
                    <Card
                        key={resume.id}
                        className="hover:shadow-md transition-shadow"
                    >
                        <CardHeader className="pb-3">
                            <div className="flex items-start justify-between">
                                <div className="flex items-center space-x-2">
                                    <FileText className="w-5 h-5 text-primary" />
                                    <CardTitle className="text-lg truncate">
                                        {resume.name}
                                    </CardTitle>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() =>
                                            toggleFavorite(resume.id)
                                        }
                                    >
                                        <Star
                                            className={`w-4 h-4 ${
                                                resume.isFavorite
                                                    ? "fill-yellow-400 text-yellow-400"
                                                    : "text-gray-400"
                                            }`}
                                        />
                                    </Button>
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" size="sm">
                                                <MoreVertical className="w-4 h-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent>
                                            <Link
                                                to={`/dashboard/resume/builder/${resume.id}`}
                                            >
                                                <DropdownMenuItem>
                                                    <Edit className="w-4 h-4 mr-2" />
                                                    Edit
                                                </DropdownMenuItem>
                                            </Link>
                                            <DropdownMenuItem
                                                onClick={() =>
                                                    handleDuplicate(resume)
                                                }
                                            >
                                                <Copy className="w-4 h-4 mr-2" />
                                                Duplicate
                                            </DropdownMenuItem>
                                            <DropdownMenuItem>
                                                <Download className="w-4 h-4 mr-2" />
                                                Download PDF
                                            </DropdownMenuItem>
                                            <DropdownMenuItem
                                                onClick={() =>
                                                    handleDelete(resume.id)
                                                }
                                                className="text-red-600"
                                            >
                                                <Trash2 className="w-4 h-4 mr-2" />
                                                Delete
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <Badge
                                    className={getStatusColor(resume.status)}
                                >
                                    {resume.status.charAt(0).toUpperCase() +
                                        resume.status.slice(1)}
                                </Badge>
                                <span className="text-sm text-gray-500">
                                    Template: {resume.template}
                                </span>
                            </div>

                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                    <span className="text-sm text-gray-500">
                                        Score:
                                    </span>
                                    <span
                                        className={`font-semibold ${getScoreColor(
                                            resume.score
                                        )}`}
                                    >
                                        {resume.score}%
                                    </span>
                                </div>
                                <div className="flex items-center space-x-1 text-sm text-gray-500">
                                    <Download className="w-3 h-3" />
                                    <span>{resume.downloads}</span>
                                </div>
                            </div>

                            <div className="flex items-center space-x-2 text-sm text-gray-500">
                                <Calendar className="w-3 h-3" />
                                <span>
                                    Modified:{" "}
                                    {new Date(
                                        resume.lastModified
                                    ).toLocaleDateString()}
                                </span>
                            </div>

                            <div className="flex space-x-2 pt-2">
                                <Link
                                    to={`/dashboard/resume/builder/${resume.id}`}
                                    className="flex-1"
                                >
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="w-full"
                                    >
                                        <Edit className="w-4 h-4 mr-2" />
                                        Edit
                                    </Button>
                                </Link>
                                <Button variant="outline" size="sm">
                                    <Eye className="w-4 h-4 mr-2" />
                                    Preview
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>

            {filteredResumes.length === 0 && (
                <div className="text-center py-12">
                    <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                        No resumes found
                    </h3>
                    <p className="text-gray-600 mb-4">
                        {searchTerm || filterStatus !== "all"
                            ? "Try adjusting your search or filter criteria."
                            : "Get started by creating your first resume."}
                    </p>
                    <Link to="/dashboard/resume/builder">
                        <Button>
                            <Plus className="w-4 h-4 mr-2" />
                            Create New Resume
                        </Button>
                    </Link>
                </div>
            )}
        </div>
    );
};

export default ResumeHub;
