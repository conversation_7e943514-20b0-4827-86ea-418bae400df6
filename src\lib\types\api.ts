// Base API Response Structure
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    errors?: string[];
    timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}

// Error Response Structure
export interface ApiError {
    status: number;
    message: string;
    code?: string;
    details?: Record<string, any>;
    timestamp: string;
}

// Authentication Types
export interface LoginRequest {
    email: string;
    password: string;
    rememberMe?: boolean;
}

export interface LoginResponse {
    user: User;
    token: string;
    refreshToken: string;
    expiresIn: number;
}

export interface User {
    id: string;
    clerkUserId: string;
    email: string;
    firstName: string;
    lastName: string;
    avatar?: string;
    phone?: string;
    dateOfBirth?: string;
    location?: {
        city: string;
        state: string;
        country: string;
    };
    preferences: UserPreferences;
    subscription: SubscriptionInfo;
    createdAt: string;
    updatedAt: string;
}

export interface UserPreferences {
    theme: "light" | "dark" | "system";
    language: string;
    timezone: string;
    emailNotifications: boolean;
    pushNotifications: boolean;
    jobAlerts: boolean;
    marketingEmails: boolean;
}

export interface SubscriptionInfo {
    plan: "FREE" | "PREMIUM" | "ENTERPRISE";
    status: "ACTIVE" | "CANCELLED" | "EXPIRED" | "TRIAL";
    startDate: string;
    endDate?: string;
    features: string[];
    usage: {
        resumeAnalyses: number;
        jobApplications: number;
        interviewSessions: number;
        maxResumeAnalyses: number;
        maxJobApplications: number;
        maxInterviewSessions: number;
    };
}

// Resume Types
export interface Resume {
    id: string;
    userId: string;
    title: string;
    fileName: string;
    fileUrl: string;
    fileSize: number;
    mimeType: string;
    isPrimary: boolean;
    status: "PROCESSING" | "COMPLETED" | "FAILED";
    parsedData: ParsedResumeData;
    analysis: ResumeAnalysis;
    shareToken?: string;
    shareExpiresAt?: string;
    createdAt: string;
    updatedAt: string;
}

export interface ParsedResumeData {
    personalInfo: {
        name: string;
        email: string;
        phone: string;
        location: string;
        linkedin?: string;
        github?: string;
        website?: string;
    };
    summary: string;
    experience: WorkExperience[];
    education: Education[];
    skills: Skill[];
    certifications: Certification[];
    projects: Project[];
    languages: Language[];
}

export interface WorkExperience {
    id: string;
    company: string;
    position: string;
    location: string;
    startDate: string;
    endDate?: string;
    current: boolean;
    description: string;
    achievements: string[];
    technologies: string[];
}

export interface Education {
    id: string;
    institution: string;
    degree: string;
    field: string;
    location: string;
    startDate: string;
    endDate?: string;
    gpa?: number;
    honors?: string[];
    relevantCourses: string[];
}

export interface Skill {
    id: string;
    name: string;
    category: string;
    proficiency: "BEGINNER" | "INTERMEDIATE" | "ADVANCED" | "EXPERT";
    yearsOfExperience?: number;
    lastUsed?: string;
    endorsed: boolean;
}

export interface Certification {
    id: string;
    name: string;
    issuer: string;
    issueDate: string;
    expiryDate?: string;
    credentialId?: string;
    credentialUrl?: string;
}

export interface Project {
    id: string;
    name: string;
    description: string;
    technologies: string[];
    startDate: string;
    endDate?: string;
    url?: string;
    githubUrl?: string;
}

export interface Language {
    id: string;
    name: string;
    proficiency: "BASIC" | "CONVERSATIONAL" | "FLUENT" | "NATIVE";
}

export interface ResumeAnalysis {
    overallScore: number;
    sections: {
        personalInfo: SectionAnalysis;
        summary: SectionAnalysis;
        experience: SectionAnalysis;
        education: SectionAnalysis;
        skills: SectionAnalysis;
    };
    suggestions: Suggestion[];
    keywords: KeywordAnalysis;
    atsCompatibility: ATSAnalysis;
    industryAlignment: IndustryAnalysis;
    generatedAt: string;
}

export interface SectionAnalysis {
    score: number;
    feedback: string;
    suggestions: string[];
    missing: string[];
    strengths: string[];
}

export interface Suggestion {
    type: "CONTENT" | "FORMAT" | "KEYWORD" | "STRUCTURE";
    priority: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
    section: string;
    current: string;
    suggested: string;
    reason: string;
    impact: number; // 1-10
}

export interface KeywordAnalysis {
    total: number;
    matched: string[];
    missing: string[];
    density: number;
    recommendations: string[];
}

export interface ATSAnalysis {
    score: number;
    issues: string[];
    recommendations: string[];
    compatibility: "POOR" | "FAIR" | "GOOD" | "EXCELLENT";
}

export interface IndustryAnalysis {
    targetIndustry: string;
    alignmentScore: number;
    relevantSkills: string[];
    missingSkills: string[];
    recommendations: string[];
}

export interface CreateResumeRequest {
    title: string;
    file: File;
}

export interface UpdateResumeRequest {
    title?: string;
    parsedData?: Partial<ParsedResumeData>;
}

export interface ResumeListResponse extends PaginatedResponse<Resume> {}

export interface ResumeShareRequest {
    expiresIn?: number; // hours
    password?: string;
}

export interface ResumeShareResponse {
    shareUrl: string;
    token: string;
    expiresAt: string;
}

// Job Types
export interface Job {
    id: string;
    title: string;
    company: Company;
    location: JobLocation;
    description: string;
    requirements: string[];
    responsibilities: string[];
    benefits: string[];
    salaryRange: SalaryRange;
    jobType:
        | "FULL_TIME"
        | "PART_TIME"
        | "CONTRACT"
        | "FREELANCE"
        | "INTERNSHIP";
    experienceLevel: "ENTRY" | "MID" | "SENIOR" | "EXECUTIVE";
    remote: boolean;
    skills: string[];
    tags: string[];
    postedDate: string;
    applicationDeadline?: string;
    applicationUrl: string;
    source: string;
    matchScore?: number;
    saved: boolean;
    applied: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface Company {
    id: string;
    name: string;
    logo?: string;
    website?: string;
    industry: string;
    size: string;
    description: string;
    culture: string[];
    benefits: string[];
    locations: string[];
    rating?: number;
    reviewCount?: number;
}

export interface JobLocation {
    city: string;
    state: string;
    country: string;
    remote: boolean;
    hybrid: boolean;
}

export interface SalaryRange {
    min: number;
    max: number;
    currency: string;
    period: "HOURLY" | "MONTHLY" | "YEARLY";
}

export interface JobSearchRequest {
    query?: string;
    location?: string;
    jobType?: string[];
    experienceLevel?: string[];
    salaryMin?: number;
    salaryMax?: number;
    remote?: boolean;
    skills?: string[];
    company?: string;
    postedWithin?: number; // days
    page?: number;
    limit?: number;
    sortBy?: "relevance" | "date" | "salary" | "match_score";
    sortOrder?: "asc" | "desc";
}

export interface JobSearchResponse extends PaginatedResponse<Job> {
    filters: {
        locations: string[];
        companies: string[];
        jobTypes: string[];
        experienceLevels: string[];
        skills: string[];
        salaryRanges: SalaryRange[];
    };
    searchMetadata: {
        query: string;
        totalResults: number;
        searchTime: number;
        suggestions: string[];
    };
}

export interface SaveJobRequest {
    jobId: string;
    notes?: string;
}

export interface JobRecommendationsResponse extends ApiResponse<Job[]> {
    recommendationReasons: Record<string, string[]>;
    refreshedAt: string;
}
