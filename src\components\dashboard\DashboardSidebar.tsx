import React from "react";
import { Link, useLocation } from "react-router-dom";
import {
    Sidebar,
    SidebarContent,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuItem,
    SidebarMenuButton,
    SidebarFooter,
} from "@/components/ui/sidebar";
import { useUser, useClerk } from "@clerk/clerk-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import {
    Briefcase,
    Home,
    FileText,
    Search,
    Settings,
    User,
    BarChart3,
    LogOut,
    Bell,
    Sparkles,
} from "lucide-react";

const DashboardSidebar = () => {
    const { user } = useUser();
    const { signOut } = useClerk();
    const location = useLocation();

    const navigationItems = [
        { title: "Dashboard", url: "/dashboard", icon: Home },
        { title: "Resume Hub", url: "/dashboard/resume", icon: FileText },
        { title: "Job Search", url: "/dashboard/jobs", icon: Search },
        {
            title: "AI Assistant",
            url: "/dashboard/ai-assistant",
            icon: Spark<PERSON>,
        },
        { title: "Analytics", url: "/dashboard/analytics", icon: BarChart3 },
        { title: "Profile", url: "/dashboard/profile", icon: User },
        { title: "Settings", url: "/dashboard/settings", icon: Settings },
    ];

    const isActive = (url: string) => {
        if (url === "/dashboard") {
            return location.pathname === "/dashboard";
        }
        return location.pathname.startsWith(url);
    };

    // Mock profile strength for now
    const profileStrength = 75;

    return (
        <Sidebar className="border-r border-border bg-white backdrop-blur-sm">
            <SidebarHeader className="p-6 border-b border-border bg-gradient-to-r from-primary/5 to-primary/10">
                <div className="flex items-center mb-6">
                    <div className="w-10 h-10 bg-gradient-to-r from-primary to-primary-hover rounded-xl flex items-center justify-center shadow-lg">
                        <Briefcase className="w-5 h-5 text-white" />
                    </div>
                    <span className="ml-3 text-xl font-jakarta font-bold text-text-primary">
                        CareerAlgo
                    </span>
                </div>

                {/* User Profile Summary */}
                <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                        <Avatar className="w-12 h-12 border-2 border-white shadow-md">
                            <AvatarImage src={user?.imageUrl} />
                            <AvatarFallback className="bg-gradient-to-r from-primary to-primary-hover text-white font-medium text-lg">
                                {user?.firstName?.charAt(0)}
                                {user?.lastName?.charAt(0)}
                            </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                            <div className="font-semibold text-text-primary truncate">
                                {user?.firstName} {user?.lastName}
                            </div>
                            <div className="text-sm text-text-secondary truncate">
                                Software Engineer
                            </div>
                        </div>
                    </div>

                    {/* Profile Strength */}
                    <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                            <span className="text-text-secondary font-medium">
                                Profile Strength
                            </span>
                            <span className="text-primary font-semibold">
                                {profileStrength}%
                            </span>
                        </div>
                        <Progress
                            value={profileStrength}
                            className="h-2 bg-gray-200"
                        />
                    </div>
                </div>
            </SidebarHeader>

            <SidebarContent className="p-4">
                <SidebarMenu>
                    {navigationItems.map((item) => (
                        <SidebarMenuItem key={item.title}>
                            <SidebarMenuButton
                                asChild
                                className={`w-full justify-start h-12 mb-1 rounded-xl transition-all duration-200 ${
                                    isActive(item.url)
                                        ? "bg-gradient-to-r from-primary to-primary-hover text-white shadow-lg transform scale-[1.02]"
                                        : "text-text-secondary hover:text-text-primary hover:bg-gray-50 hover:scale-[1.01]"
                                }`}
                            >
                                <Link
                                    to={item.url}
                                    className="flex items-center space-x-3"
                                >
                                    <item.icon className="w-5 h-5" />
                                    <span className="font-medium">
                                        {item.title}
                                    </span>
                                </Link>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    ))}
                </SidebarMenu>

                {/* Quick Actions */}
                <div className="mt-8 space-y-4">
                    <div className="px-3">
                        <h3 className="text-xs font-semibold text-text-muted uppercase tracking-wider">
                            Quick Actions
                        </h3>
                    </div>

                    <div className="space-y-3">
                        <button className="w-full bg-gradient-to-r from-primary to-primary-hover text-white rounded-xl p-4 text-sm font-semibold hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200">
                            📄 Upload Resume
                        </button>
                        <button className="w-full bg-gradient-to-r from-success to-green-600 text-white rounded-xl p-4 text-sm font-semibold hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200">
                            🎯 Find Jobs
                        </button>
                    </div>
                </div>

                {/* Recent Activity */}
                <div className="mt-8 space-y-4">
                    <div className="px-3">
                        <h3 className="text-xs font-semibold text-text-muted uppercase tracking-wider">
                            Recent Activity
                        </h3>
                    </div>

                    <div className="space-y-3">
                        <div className="flex items-center space-x-3 px-3 py-3 rounded-xl hover:bg-gray-50 cursor-pointer transition-colors">
                            <div className="w-3 h-3 bg-primary rounded-full animate-pulse"></div>
                            <div className="flex-1 min-w-0">
                                <div className="text-sm font-medium text-text-primary truncate">
                                    Resume analyzed
                                </div>
                                <div className="text-xs text-text-muted">
                                    2 hours ago
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center space-x-3 px-3 py-3 rounded-xl hover:bg-gray-50 cursor-pointer transition-colors">
                            <div className="w-3 h-3 bg-success rounded-full"></div>
                            <div className="flex-1 min-w-0">
                                <div className="text-sm font-medium text-text-primary truncate">
                                    Job application sent
                                </div>
                                <div className="text-xs text-text-muted">
                                    1 day ago
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </SidebarContent>

            <SidebarFooter className="p-4 border-t border-border bg-gray-50/50">
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton className="w-full justify-start h-11 text-text-secondary hover:text-text-primary hover:bg-white rounded-xl transition-all duration-200">
                            <Bell className="w-5 h-5" />
                            <span className="font-medium">Notifications</span>
                            <span className="ml-auto bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center animate-pulse">
                                3
                            </span>
                        </SidebarMenuButton>
                    </SidebarMenuItem>

                    <SidebarMenuItem>
                        <SidebarMenuButton
                            onClick={() => signOut()}
                            className="w-full justify-start h-11 text-text-secondary hover:text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200"
                        >
                            <LogOut className="w-5 h-5" />
                            <span className="font-medium">Sign Out</span>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarFooter>
        </Sidebar>
    );
};

export default DashboardSidebar;
