
import React from 'react';
import { Check } from "lucide-react";

const TrustBadges = () => {
  return (
    <div className="flex flex-wrap items-center justify-center gap-6 py-8">
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <div className="w-8 h-8 bg-brand-green-500 rounded-full flex items-center justify-center">
          <Check size={16} className="text-white" />
        </div>
        <span className="font-medium">SSL Secured</span>
      </div>
      
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <div className="w-8 h-8 bg-brand-blue-500 rounded-full flex items-center justify-center">
          <span className="text-white font-bold text-xs">G</span>
        </div>
        <span className="font-medium">GDPR Compliant</span>
      </div>
      
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <div className="w-8 h-8 bg-brand-purple-500 rounded-full flex items-center justify-center">
          <span className="text-white font-bold text-xs">$</span>
        </div>
        <span className="font-medium">30-Day Guarantee</span>
      </div>
      
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <div className="w-8 h-8 bg-brand-orange-500 rounded-full flex items-center justify-center">
          <span className="text-white font-bold text-xs">24</span>
        </div>
        <span className="font-medium">24/7 Support</span>
      </div>
    </div>
  );
};

export default TrustBadges;
