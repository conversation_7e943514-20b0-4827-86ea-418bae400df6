import { api } from '../client';
import { API_ENDPOINTS } from '../endpoints';
import { 
    ApiResponse, 
    PaginatedResponse, 
    Resume, 
    CreateResumeRequest, 
    UpdateResumeRequest,
    ResumeShareRequest,
    ResumeShareResponse 
} from '@/lib/types/api';

// Additional Resume Types
export interface ResumeTemplate {
    id: string;
    name: string;
    description: string;
    category: string;
    previewUrl: string;
    isPremium: boolean;
    tags: string[];
}

export interface ResumeExportOptions {
    format: 'PDF' | 'DOCX' | 'HTML' | 'TXT';
    template?: string;
    includeAnalysis?: boolean;
}

// Pagination Parameters
export interface PaginationParams {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

// Resume API Service Class
export class ResumeAPI {
    /**
     * Get all user resumes with pagination
     */
    async getResumes(params?: PaginationParams): Promise<PaginatedResponse<Resume>> {
        const response = await api.get<PaginatedResponse<Resume>>(
            API_ENDPOINTS.resumes.list,
            { params }
        );
        return response.data;
    }

    /**
     * Get specific resume by ID
     */
    async getResume(id: string): Promise<ApiResponse<Resume>> {
        const response = await api.get<ApiResponse<Resume>>(
            API_ENDPOINTS.resumes.get(id)
        );
        return response.data;
    }

    /**
     * Create new resume
     */
    async createResume(data: CreateResumeRequest): Promise<ApiResponse<Resume>> {
        const formData = new FormData();
        formData.append('title', data.title);
        formData.append('file', data.file);

        const response = await api.post<ApiResponse<Resume>>(
            API_ENDPOINTS.resumes.create,
            formData,
            {
                headers: { 'Content-Type': 'multipart/form-data' },
            }
        );
        return response.data;
    }

    /**
     * Update resume
     */
    async updateResume(id: string, data: UpdateResumeRequest): Promise<ApiResponse<Resume>> {
        const response = await api.put<ApiResponse<Resume>>(
            API_ENDPOINTS.resumes.update(id),
            data
        );
        return response.data;
    }

    /**
     * Delete resume
     */
    async deleteResume(id: string): Promise<ApiResponse<void>> {
        const response = await api.delete<ApiResponse<void>>(
            API_ENDPOINTS.resumes.delete(id)
        );
        return response.data;
    }

    /**
     * Set resume as primary
     */
    async setPrimaryResume(id: string): Promise<ApiResponse<Resume>> {
        const response = await api.post<ApiResponse<Resume>>(
            API_ENDPOINTS.resumes.setPrimary(id)
        );
        return response.data;
    }

    /**
     * Duplicate resume
     */
    async duplicateResume(id: string, title?: string): Promise<ApiResponse<Resume>> {
        const response = await api.post<ApiResponse<Resume>>(
            API_ENDPOINTS.resumes.duplicate(id),
            { title }
        );
        return response.data;
    }

    /**
     * Share resume
     */
    async shareResume(id: string, data: ResumeShareRequest): Promise<ResumeShareResponse> {
        const response = await api.post<ResumeShareResponse>(
            API_ENDPOINTS.resumes.share(id),
            data
        );
        return response.data;
    }

    /**
     * Download resume
     */
    async downloadResume(id: string, options?: ResumeExportOptions): Promise<Blob> {
        const response = await api.get(
            API_ENDPOINTS.resumes.download(id),
            {
                params: options,
                responseType: 'blob',
            }
        );
        return response.data;
    }

    /**
     * Search resumes
     */
    async searchResumes(query: string, params?: PaginationParams): Promise<PaginatedResponse<Resume>> {
        const response = await api.get<PaginatedResponse<Resume>>(
            API_ENDPOINTS.resumes.search,
            {
                params: {
                    query,
                    ...params,
                },
            }
        );
        return response.data;
    }

    /**
     * Get resume templates
     */
    async getTemplates(): Promise<ApiResponse<ResumeTemplate[]>> {
        const response = await api.get<ApiResponse<ResumeTemplate[]>>(
            `${API_ENDPOINTS.resumes.list}/templates`
        );
        return response.data;
    }

    /**
     * Create resume from template
     */
    async createFromTemplate(templateId: string, title: string): Promise<ApiResponse<Resume>> {
        const response = await api.post<ApiResponse<Resume>>(
            `${API_ENDPOINTS.resumes.create}/from-template`,
            { templateId, title }
        );
        return response.data;
    }

    /**
     * Update resume content
     */
    async updateResumeContent(id: string, content: any): Promise<ApiResponse<Resume>> {
        const response = await api.patch<ApiResponse<Resume>>(
            `${API_ENDPOINTS.resumes.update(id)}/content`,
            { content }
        );
        return response.data;
    }

    /**
     * Get resume analysis
     */
    async getResumeAnalysis(id: string): Promise<ApiResponse<any>> {
        const response = await api.get<ApiResponse<any>>(
            `${API_ENDPOINTS.resumes.get(id)}/analysis`
        );
        return response.data;
    }

    /**
     * Request resume analysis
     */
    async requestAnalysis(id: string, options?: {
        targetRole?: string;
        targetCompany?: string;
        analysisType?: 'BASIC' | 'DETAILED' | 'ATS_OPTIMIZATION' | 'INDUSTRY_SPECIFIC';
    }): Promise<ApiResponse<any>> {
        const response = await api.post<ApiResponse<any>>(
            `${API_ENDPOINTS.resumes.get(id)}/analyze`,
            options
        );
        return response.data;
    }

    /**
     * Get resume versions/history
     */
    async getResumeVersions(id: string): Promise<ApiResponse<any[]>> {
        const response = await api.get<ApiResponse<any[]>>(
            `${API_ENDPOINTS.resumes.get(id)}/versions`
        );
        return response.data;
    }

    /**
     * Restore resume version
     */
    async restoreVersion(id: string, versionId: string): Promise<ApiResponse<Resume>> {
        const response = await api.post<ApiResponse<Resume>>(
            `${API_ENDPOINTS.resumes.get(id)}/versions/${versionId}/restore`
        );
        return response.data;
    }

    /**
     * Export resume to different formats
     */
    async exportResume(id: string, options: ResumeExportOptions): Promise<Blob> {
        const response = await api.post(
            `${API_ENDPOINTS.resumes.get(id)}/export`,
            options,
            { responseType: 'blob' }
        );
        return response.data;
    }

    /**
     * Get resume sharing settings
     */
    async getSharingSettings(id: string): Promise<ApiResponse<any>> {
        const response = await api.get<ApiResponse<any>>(
            `${API_ENDPOINTS.resumes.get(id)}/sharing`
        );
        return response.data;
    }

    /**
     * Update resume sharing settings
     */
    async updateSharingSettings(id: string, settings: any): Promise<ApiResponse<any>> {
        const response = await api.put<ApiResponse<any>>(
            `${API_ENDPOINTS.resumes.get(id)}/sharing`,
            settings
        );
        return response.data;
    }

    /**
     * Revoke resume sharing
     */
    async revokeSharing(id: string): Promise<ApiResponse<void>> {
        const response = await api.delete<ApiResponse<void>>(
            `${API_ENDPOINTS.resumes.get(id)}/sharing`
        );
        return response.data;
    }
}

// Export singleton instance
export const resumeApi = new ResumeAPI();
