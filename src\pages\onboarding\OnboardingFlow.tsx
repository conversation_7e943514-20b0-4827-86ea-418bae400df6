import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth, useUser } from "@clerk/clerk-react";
import { Navigate } from "react-router-dom";

const OnboardingFlow = () => {
    const { user } = useUser();
    const [currentStep, setCurrentStep] = useState(1);
    const [onboardingData, setOnboardingData] = useState({
        jobTitle: "",
        industry: "",
        experience: "",
        goals: "",
    });

    // Redirect if user has already completed onboarding
    if (user?.publicMetadata?.onboardingCompleted) {
        return <Navigate to="/dashboard" replace />;
    }

    const handleNext = () => {
        if (currentStep < 3) {
            setCurrentStep(currentStep + 1);
        } else {
            // Complete onboarding
            console.log("Onboarding completed:", onboardingData);
            // In a real app, you would save this data and mark onboarding as complete
        }
    };

    const handlePrev = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };

    const renderStep = () => {
        switch (currentStep) {
            case 1:
                return (
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="jobTitle">Current Job Title</Label>
                            <Input
                                id="jobTitle"
                                value={onboardingData.jobTitle}
                                onChange={(e) =>
                                    setOnboardingData({
                                        ...onboardingData,
                                        jobTitle: e.target.value,
                                    })
                                }
                                placeholder="e.g., Software Engineer"
                            />
                        </div>
                        <div>
                            <Label htmlFor="industry">Industry</Label>
                            <Input
                                id="industry"
                                value={onboardingData.industry}
                                onChange={(e) =>
                                    setOnboardingData({
                                        ...onboardingData,
                                        industry: e.target.value,
                                    })
                                }
                                placeholder="e.g., Technology"
                            />
                        </div>
                    </div>
                );
            case 2:
                return (
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="experience">
                                Years of Experience
                            </Label>
                            <Input
                                id="experience"
                                value={onboardingData.experience}
                                onChange={(e) =>
                                    setOnboardingData({
                                        ...onboardingData,
                                        experience: e.target.value,
                                    })
                                }
                                placeholder="e.g., 5"
                            />
                        </div>
                    </div>
                );
            case 3:
                return (
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="goals">Career Goals</Label>
                            <Input
                                id="goals"
                                value={onboardingData.goals}
                                onChange={(e) =>
                                    setOnboardingData({
                                        ...onboardingData,
                                        goals: e.target.value,
                                    })
                                }
                                placeholder="What are your career aspirations?"
                            />
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-primary/10 to-secondary/20 flex items-center justify-center p-4">
            <Card className="w-full max-w-md">
                <CardHeader>
                    <CardTitle className="text-center">
                        Welcome, {user?.firstName}!
                    </CardTitle>
                    <p className="text-center text-text-secondary">
                        Let's set up your profile (Step {currentStep} of 3)
                    </p>
                </CardHeader>
                <CardContent className="space-y-6">
                    {renderStep()}

                    <div className="flex justify-between">
                        <Button
                            variant="outline"
                            onClick={handlePrev}
                            disabled={currentStep === 1}
                        >
                            Previous
                        </Button>
                        <Button onClick={handleNext}>
                            {currentStep === 3 ? "Complete" : "Next"}
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};

export default OnboardingFlow;
