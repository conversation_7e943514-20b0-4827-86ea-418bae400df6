import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Play, Star } from "lucide-react";
import HeroGraphics from "@/components/landing-page/HeroGraphics";

interface HeroSectionProps {
    onCTAClick: (action: string) => void;
}

const HeroSection = ({ onCTAClick }: HeroSectionProps) => {
    return (
        <section className="pt-24 pb-16 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                    {/* Left side - Text content */}
                    <div className="text-center lg:text-left">
                        <Badge
                            variant="outline"
                            className="mb-6 text-brand-blue-500 border-brand-blue-200 bg-brand-blue-50"
                        >
                            AI-Powered Career Transformation
                        </Badge>

                        <h1 className="text-4xl md:text-6xl font-jakarta font-bold text-slate-900 mb-6 leading-tight">
                            Land Your Dream Job
                            <br />
                            <span className="text-transparent bg-gradient-to-r from-brand-blue-500 to-purple-600 bg-clip-text">
                                3x Faster with AI
                            </span>
                        </h1>

                        <p className="text-xl text-gray-600 mb-8 max-w-3xl leading-relaxed">
                            Transform your resume, discover perfect
                            opportunities, and ace interviews with our
                            intelligent career platform trusted by 50,000+
                            professionals
                        </p>

                        <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
                            <Button
                                size="lg"
                                onClick={() => onCTAClick("signup")}
                                className="bg-brand-blue-500 hover:bg-brand-blue-600 text-white px-8 py-4 text-lg font-medium rounded-lg transition-all duration-200 hover:scale-105 shadow-lg"
                            >
                                Start Free Today
                            </Button>
                            <Button
                                size="lg"
                                variant="outline"
                                onClick={() => onCTAClick("demo")}
                                className="border-brand-blue-500 text-brand-blue-500 hover:bg-brand-blue-50 px-8 py-4 text-lg font-medium rounded-lg transition-all duration-200 flex items-center gap-2"
                            >
                                <Play size={20} />
                                Watch 2-Min Demo
                            </Button>
                        </div>

                        <div className="flex items-center justify-center lg:justify-start gap-2 text-sm text-gray-600">
                            <div className="flex text-yellow-400">
                                {[...Array(5)].map((_, i) => (
                                    <Star
                                        key={i}
                                        size={16}
                                        fill="currentColor"
                                    />
                                ))}
                            </div>
                            <span className="font-medium">
                                4.9/5 from 2,847 reviews
                            </span>
                        </div>
                    </div>

                    {/* Right side - Hero Graphics */}
                    <div className="lg:pl-8">
                        <HeroGraphics />
                    </div>
                </div>
            </div>
        </section>
    );
};

export default HeroSection;
