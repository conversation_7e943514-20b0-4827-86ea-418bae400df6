import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Bell, Shield, User, Mail, Globe } from "lucide-react";

const Settings = () => {
    return (
        <div className="space-y-6">
            <div>
                <h1 className="text-3xl font-jakarta font-bold text-text-primary">
                    Settings
                </h1>
                <p className="text-text-secondary mt-2">
                    Manage your account preferences and privacy settings
                </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Notifications */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-lg font-jakarta flex items-center">
                            <Bell className="w-5 h-5 mr-2" />
                            Notifications
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <Label
                                    htmlFor="job-alerts"
                                    className="text-sm font-medium"
                                >
                                    Job Alerts
                                </Label>
                                <p className="text-xs text-text-muted">
                                    Get notified about new job matches
                                </p>
                            </div>
                            <Switch id="job-alerts" defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                            <div>
                                <Label
                                    htmlFor="application-updates"
                                    className="text-sm font-medium"
                                >
                                    Application Updates
                                </Label>
                                <p className="text-xs text-text-muted">
                                    Updates on your job applications
                                </p>
                            </div>
                            <Switch id="application-updates" defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                            <div>
                                <Label
                                    htmlFor="ai-insights"
                                    className="text-sm font-medium"
                                >
                                    AI Insights
                                </Label>
                                <p className="text-xs text-text-muted">
                                    Weekly career insights and tips
                                </p>
                            </div>
                            <Switch id="ai-insights" defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                            <div>
                                <Label
                                    htmlFor="marketing"
                                    className="text-sm font-medium"
                                >
                                    Marketing Communications
                                </Label>
                                <p className="text-xs text-text-muted">
                                    Product updates and feature announcements
                                </p>
                            </div>
                            <Switch id="marketing" />
                        </div>
                    </CardContent>
                </Card>

                {/* Privacy */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-lg font-jakarta flex items-center">
                            <Shield className="w-5 h-5 mr-2" />
                            Privacy
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <Label
                                    htmlFor="profile-visibility"
                                    className="text-sm font-medium"
                                >
                                    Profile Visibility
                                </Label>
                                <p className="text-xs text-text-muted">
                                    Make your profile visible to recruiters
                                </p>
                            </div>
                            <Switch id="profile-visibility" defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                            <div>
                                <Label
                                    htmlFor="data-sharing"
                                    className="text-sm font-medium"
                                >
                                    Anonymous Data Sharing
                                </Label>
                                <p className="text-xs text-text-muted">
                                    Help improve our AI recommendations
                                </p>
                            </div>
                            <Switch id="data-sharing" defaultChecked />
                        </div>

                        <div className="flex items-center justify-between">
                            <div>
                                <Label
                                    htmlFor="activity-tracking"
                                    className="text-sm font-medium"
                                >
                                    Activity Tracking
                                </Label>
                                <p className="text-xs text-text-muted">
                                    Track usage for personalized experience
                                </p>
                            </div>
                            <Switch id="activity-tracking" defaultChecked />
                        </div>
                    </CardContent>
                </Card>

                {/* Account */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-lg font-jakarta flex items-center">
                            <User className="w-5 h-5 mr-2" />
                            Account
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <Button
                            variant="outline"
                            className="w-full justify-start"
                        >
                            Change Password
                        </Button>
                        <Button
                            variant="outline"
                            className="w-full justify-start"
                        >
                            Download My Data
                        </Button>
                        <Button
                            variant="outline"
                            className="w-full justify-start text-error hover:text-error"
                        >
                            Delete Account
                        </Button>
                    </CardContent>
                </Card>

                {/* Preferences */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-lg font-jakarta flex items-center">
                            <Globe className="w-5 h-5 mr-2" />
                            Preferences
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <Label
                                    htmlFor="auto-apply"
                                    className="text-sm font-medium"
                                >
                                    Auto-Apply to Jobs
                                </Label>
                                <p className="text-xs text-text-muted">
                                    Automatically apply to highly matched jobs
                                </p>
                            </div>
                            <Switch id="auto-apply" />
                        </div>

                        <div className="flex items-center justify-between">
                            <div>
                                <Label
                                    htmlFor="resume-tips"
                                    className="text-sm font-medium"
                                >
                                    Resume Tips
                                </Label>
                                <p className="text-xs text-text-muted">
                                    Show improvement suggestions on dashboard
                                </p>
                            </div>
                            <Switch id="resume-tips" defaultChecked />
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default Settings;
