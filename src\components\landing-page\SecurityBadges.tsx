
import React from 'react';
import { Shield, Lock, CheckCircle, Globe } from "lucide-react";

const SecurityBadges = () => {
  return (
    <section className="py-12 bg-gray-50 border-y">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h3 className="text-lg font-semibold text-slate-900 mb-2">Trusted & Secure Platform</h3>
          <p className="text-gray-600">Your data is protected by enterprise-grade security</p>
        </div>
        
        <div className="flex items-center justify-center gap-8 md:gap-12 flex-wrap">
          {[
            { icon: Shield, label: "SOC 2 Certified", description: "Type II Compliance" },
            { icon: Lock, label: "256-bit SSL", description: "Bank-level Encryption" },
            { icon: CheckCircle, label: "GDPR Compliant", description: "EU Data Protection" },
            { icon: Globe, label: "ISO 27001", description: "Security Standard" }
          ].map((badge, index) => (
            <div key={index} className="flex items-center gap-3 group cursor-pointer">
              <div className="p-2 rounded-lg bg-green-50 group-hover:bg-green-100 transition-colors">
                <badge.icon className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <div className="text-sm font-semibold text-slate-900">{badge.label}</div>
                <div className="text-xs text-gray-500">{badge.description}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SecurityBadges;
