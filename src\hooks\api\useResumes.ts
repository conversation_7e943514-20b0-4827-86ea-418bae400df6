import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { resumeApi, PaginationParams, ResumeExportOptions } from '@/lib/api/services/resumeApi';
import { CreateResumeRequest, UpdateResumeRequest, ResumeShareRequest } from '@/lib/types/api';
import { toast } from 'sonner';

// Query Keys
export const resumeQueryKeys = {
    all: ['resumes'] as const,
    list: (params?: PaginationParams) => [...resumeQueryKeys.all, 'list', params] as const,
    detail: (id: string) => [...resumeQueryKeys.all, 'detail', id] as const,
    search: (query: string, params?: PaginationParams) => 
        [...resumeQueryKeys.all, 'search', query, params] as const,
    templates: () => [...resumeQueryKeys.all, 'templates'] as const,
    analysis: (id: string) => [...resumeQueryKeys.all, 'analysis', id] as const,
    versions: (id: string) => [...resumeQueryKeys.all, 'versions', id] as const,
    sharing: (id: string) => [...resumeQueryKeys.all, 'sharing', id] as const,
};

// Resume List Hook
export const useResumes = (params?: PaginationParams) => {
    return useQuery({
        queryKey: resumeQueryKeys.list(params),
        queryFn: async () => {
            const response = await resumeApi.getResumes(params);
            return response;
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
    });
};

// Single Resume Hook
export const useResume = (id: string) => {
    return useQuery({
        queryKey: resumeQueryKeys.detail(id),
        queryFn: async () => {
            const response = await resumeApi.getResume(id);
            return response.data;
        },
        enabled: !!id,
        staleTime: 5 * 60 * 1000,
        retry: 2,
    });
};

// Create Resume Hook
export const useCreateResume = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (data: CreateResumeRequest) => resumeApi.createResume(data),
        onSuccess: (response) => {
            // Invalidate and refetch resume list
            queryClient.invalidateQueries({ queryKey: resumeQueryKeys.all });
            
            toast.success('Resume created successfully');
            return response.data;
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to create resume');
        },
    });
};

// Update Resume Hook
export const useUpdateResume = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, data }: { id: string; data: UpdateResumeRequest }) => 
            resumeApi.updateResume(id, data),
        onSuccess: (response, variables) => {
            // Update the specific resume cache
            queryClient.setQueryData(
                resumeQueryKeys.detail(variables.id), 
                response.data
            );
            
            // Invalidate resume list to reflect changes
            queryClient.invalidateQueries({ queryKey: resumeQueryKeys.list() });
            
            toast.success('Resume updated successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to update resume');
        },
    });
};

// Delete Resume Hook
export const useDeleteResume = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (id: string) => resumeApi.deleteResume(id),
        onSuccess: (_, id) => {
            // Remove from cache
            queryClient.removeQueries({ queryKey: resumeQueryKeys.detail(id) });
            
            // Invalidate resume list
            queryClient.invalidateQueries({ queryKey: resumeQueryKeys.list() });
            
            toast.success('Resume deleted successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to delete resume');
        },
    });
};

// Set Primary Resume Hook
export const useSetPrimaryResume = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (id: string) => resumeApi.setPrimaryResume(id),
        onSuccess: (response, id) => {
            // Update the specific resume cache
            queryClient.setQueryData(resumeQueryKeys.detail(id), response.data);
            
            // Invalidate resume list to update primary status
            queryClient.invalidateQueries({ queryKey: resumeQueryKeys.list() });
            
            toast.success('Primary resume updated successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to set primary resume');
        },
    });
};

// Duplicate Resume Hook
export const useDuplicateResume = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, title }: { id: string; title?: string }) => 
            resumeApi.duplicateResume(id, title),
        onSuccess: (response) => {
            // Invalidate resume list to show new duplicate
            queryClient.invalidateQueries({ queryKey: resumeQueryKeys.list() });
            
            toast.success('Resume duplicated successfully');
            return response.data;
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to duplicate resume');
        },
    });
};

// Share Resume Hook
export const useShareResume = () => {
    return useMutation({
        mutationFn: ({ id, data }: { id: string; data: ResumeShareRequest }) => 
            resumeApi.shareResume(id, data),
        onSuccess: (response) => {
            toast.success('Resume shared successfully');
            return response;
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to share resume');
        },
    });
};

// Download Resume Hook
export const useDownloadResume = () => {
    return useMutation({
        mutationFn: ({ id, options }: { id: string; options?: ResumeExportOptions }) => 
            resumeApi.downloadResume(id, options),
        onSuccess: (blob, variables) => {
            // Create download link
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `resume.${variables.options?.format?.toLowerCase() || 'pdf'}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
            toast.success('Resume downloaded successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to download resume');
        },
    });
};

// Search Resumes Hook
export const useSearchResumes = (query: string, params?: PaginationParams) => {
    return useQuery({
        queryKey: resumeQueryKeys.search(query, params),
        queryFn: async () => {
            const response = await resumeApi.searchResumes(query, params);
            return response;
        },
        enabled: !!query && query.length > 0,
        staleTime: 2 * 60 * 1000, // 2 minutes
        retry: 2,
    });
};

// Resume Templates Hook
export const useResumeTemplates = () => {
    return useQuery({
        queryKey: resumeQueryKeys.templates(),
        queryFn: async () => {
            const response = await resumeApi.getTemplates();
            return response.data;
        },
        staleTime: 30 * 60 * 1000, // 30 minutes
        retry: 2,
    });
};

// Create from Template Hook
export const useCreateFromTemplate = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ templateId, title }: { templateId: string; title: string }) => 
            resumeApi.createFromTemplate(templateId, title),
        onSuccess: (response) => {
            // Invalidate resume list
            queryClient.invalidateQueries({ queryKey: resumeQueryKeys.list() });
            
            toast.success('Resume created from template successfully');
            return response.data;
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to create resume from template');
        },
    });
};

// Resume Analysis Hook
export const useResumeAnalysis = (id: string) => {
    return useQuery({
        queryKey: resumeQueryKeys.analysis(id),
        queryFn: async () => {
            const response = await resumeApi.getResumeAnalysis(id);
            return response.data;
        },
        enabled: !!id,
        staleTime: 10 * 60 * 1000, // 10 minutes
        retry: 2,
    });
};

// Request Analysis Hook
export const useRequestAnalysis = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, options }: { 
            id: string; 
            options?: {
                targetRole?: string;
                targetCompany?: string;
                analysisType?: 'BASIC' | 'DETAILED' | 'ATS_OPTIMIZATION' | 'INDUSTRY_SPECIFIC';
            }
        }) => resumeApi.requestAnalysis(id, options),
        onSuccess: (response, variables) => {
            // Invalidate analysis cache to refetch
            queryClient.invalidateQueries({ 
                queryKey: resumeQueryKeys.analysis(variables.id) 
            });
            
            toast.success('Resume analysis requested successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to request resume analysis');
        },
    });
};

// Resume Versions Hook
export const useResumeVersions = (id: string) => {
    return useQuery({
        queryKey: resumeQueryKeys.versions(id),
        queryFn: async () => {
            const response = await resumeApi.getResumeVersions(id);
            return response.data;
        },
        enabled: !!id,
        staleTime: 5 * 60 * 1000,
        retry: 2,
    });
};

// Export Resume Hook
export const useExportResume = () => {
    return useMutation({
        mutationFn: ({ id, options }: { id: string; options: ResumeExportOptions }) => 
            resumeApi.exportResume(id, options),
        onSuccess: (blob, variables) => {
            // Create download link
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `resume.${variables.options.format.toLowerCase()}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
            toast.success('Resume exported successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to export resume');
        },
    });
};
