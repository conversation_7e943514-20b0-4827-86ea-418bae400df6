import React, { useState, useRef, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    <PERSON>alogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import {
    Brain,
    MessageCircle,
    FileText,
    Target,
    TrendingUp,
    Send,
    User,
    Bot,
    Play,
    CheckCircle,
    Loader,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const AIAssistant = () => {
    const { toast } = useToast();
    const [message, setMessage] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const [chatHistory, setChatHistory] = useState([
        {
            type: "ai",
            message:
                "Hello! I'm your AI career assistant. How can I help you today?",
            time: "9:00 AM",
        },
        {
            type: "user",
            message:
                "I want to improve my resume for software engineering roles",
            time: "9:05 AM",
        },
        {
            type: "ai",
            message:
                "Great! I can help you optimize your resume. Based on current market trends for software engineering roles, here are some key areas to focus on: technical skills, project experience, and quantifiable achievements. Would you like me to analyze your current resume?",
            time: "9:06 AM",
        },
    ]);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [chatHistory]);

    const [currentInterview, setCurrentInterview] = useState(null);
    const [interviewQuestions] = useState([
        "Tell me about yourself and your background.",
        "What are your greatest strengths as a software engineer?",
        "Describe a challenging project you worked on and how you overcame obstacles.",
        "Where do you see yourself in 5 years?",
        "Why are you interested in this role and our company?",
    ]);

    const [skillsAnalysis, setSkillsAnalysis] = useState({
        currentSkills: ["JavaScript", "React", "Node.js", "Python"],
        marketDemand: ["TypeScript", "AWS", "Docker", "Kubernetes", "GraphQL"],
        recommendations: [
            "Learn TypeScript to improve JavaScript development",
            "Get AWS certification for cloud skills",
            "Practice system design for senior roles",
        ],
    });

    const aiTools = [
        {
            title: "Resume Optimizer",
            description: "Get AI-powered suggestions to improve your resume",
            icon: FileText,
            color: "bg-primary",
            action: "Optimize Resume",
        },
        {
            title: "Interview Prep",
            description: "Practice with AI-generated interview questions",
            icon: MessageCircle,
            color: "bg-success",
            action: "Start Practice",
        },
        {
            title: "Career Advisor",
            description: "Get personalized career guidance and insights",
            icon: Target,
            color: "bg-warning",
            action: "Get Advice",
        },
        {
            title: "Skills Analysis",
            description:
                "Analyze your skills and identify growth opportunities",
            icon: TrendingUp,
            color: "bg-error",
            action: "Analyze Skills",
        },
    ];

    const handleSendMessage = async () => {
        if (!message.trim() || isLoading) return;

        const userMessage = {
            type: "user",
            message: message,
            time: new Date().toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
            }),
        };

        setChatHistory((prev) => [...prev, userMessage]);
        setMessage("");
        setIsLoading(true);

        // Mock AI response with loading delay
        setTimeout(() => {
            const aiResponse = generateAIResponse(userMessage.message);
            setChatHistory((prev) => [
                ...prev,
                {
                    type: "ai",
                    message: aiResponse,
                    time: new Date().toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                    }),
                },
            ]);
            setIsLoading(false);
        }, 2000);
    };

    const generateAIResponse = (userMessage) => {
        const responses = [
            "That's a great question! Based on your profile, I'd recommend focusing on...",
            "I can help you with that. Here are some personalized suggestions for your career growth...",
            "Let me analyze that for you. From what I can see in your background...",
            "Excellent! That aligns well with current market trends. I suggest...",
            "Based on my analysis of thousands of successful profiles, here's what I recommend...",
        ];

        if (userMessage.toLowerCase().includes("resume")) {
            return "I'd be happy to help optimize your resume! Upload your current resume and I'll provide specific suggestions to improve ATS compatibility, highlight your achievements, and align with industry standards.";
        }

        if (userMessage.toLowerCase().includes("interview")) {
            return "Great! Interview preparation is crucial. I can help you practice common questions, provide feedback on your answers, and give tips specific to your target role. Would you like to start with behavioral or technical questions?";
        }

        return responses[Math.floor(Math.random() * responses.length)];
    };

    const handleResumeOptimization = () => {
        toast({
            title: "Resume Analysis Started",
            description:
                "AI is analyzing your resume for optimization opportunities...",
        });

        setTimeout(() => {
            setChatHistory((prev) => [
                ...prev,
                {
                    type: "ai",
                    message:
                        'Resume analysis complete! Here are my recommendations:\n\n1. Add more quantifiable achievements (e.g., "Increased app performance by 40%")\n2. Include relevant keywords for ATS optimization\n3. Highlight leadership and collaboration skills\n4. Add a professional summary section\n\nWould you like me to help rewrite any specific sections?',
                    time: new Date().toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                    }),
                },
            ]);
        }, 2000);
    };

    const handleStartInterview = () => {
        setCurrentInterview({
            questionIndex: 0,
            userAnswer: "",
            feedback: [],
        });
    };

    const handleInterviewAnswer = () => {
        if (!currentInterview.userAnswer.trim()) return;

        const feedback = generateInterviewFeedback(currentInterview.userAnswer);

        setCurrentInterview((prev) => ({
            ...prev,
            feedback: [
                ...prev.feedback,
                {
                    question: interviewQuestions[prev.questionIndex],
                    answer: prev.userAnswer,
                    feedback: feedback,
                },
            ],
            questionIndex: prev.questionIndex + 1,
            userAnswer: "",
        }));

        toast({
            title: "Answer Recorded",
            description: "AI feedback generated for your response!",
        });
    };

    const generateInterviewFeedback = (answer) => {
        const feedbacks = [
            "Great structure! Consider adding more specific examples to strengthen your response.",
            "Good answer! Try to quantify your achievements with numbers or percentages.",
            "Well articulated! You might want to elaborate more on the impact of your work.",
            "Nice response! Consider using the STAR method (Situation, Task, Action, Result) for better structure.",
        ];

        return feedbacks[Math.floor(Math.random() * feedbacks.length)];
    };

    const handleCareerAdvice = () => {
        setChatHistory((prev) => [
            ...prev,
            {
                type: "ai",
                message:
                    "Based on your profile and current market trends, here's my career advice:\n\n🎯 **Next Steps:**\n- Focus on senior-level responsibilities\n- Build expertise in cloud technologies\n- Consider technical leadership opportunities\n\n📈 **Growth Areas:**\n- System design and architecture\n- Team leadership and mentoring\n- Product strategy involvement\n\n💰 **Salary Expectations:**\n- Current market range: $120k - $180k\n- With additional skills: $150k - $220k\n\nWould you like specific guidance on any of these areas?",
                time: new Date().toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                }),
            },
        ]);
    };

    const handleSkillsAnalysis = () => {
        setChatHistory((prev) => [
            ...prev,
            {
                type: "ai",
                message:
                    "🔍 **Skills Analysis Complete!**\n\n**Your Current Skills:**\n✅ JavaScript, React, Node.js, Python\n\n**High-Demand Skills You're Missing:**\n🔥 TypeScript (90% of jobs require this)\n☁️ AWS/Cloud Technologies (75% of roles)\n🐳 Docker & Kubernetes (60% of senior roles)\n📊 GraphQL (45% of modern applications)\n\n**Learning Priority:**\n1. TypeScript (immediate impact)\n2. AWS Fundamentals (career growth)\n3. Docker basics (workflow improvement)\n\nWant a personalized learning roadmap?",
                time: new Date().toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                }),
            },
        ]);
    };

    const quickQuestions = [
        "How can I improve my resume?",
        "What skills are in demand for my role?",
        "How to prepare for technical interviews?",
        "What's my ideal salary range?",
        "How to transition to a new career?",
        "Tips for remote job applications?",
    ];

    return (
        <div className="h-screen flex flex-col bg-background">
            {/* Header */}
            <div className="bg-white border-b border-border px-6 flex-shrink-0 sticky top-16 z-10">
                <div className="py-2">
                    <h1 className="text-3xl font-jakarta font-bold text-text-primary ">
                        AI Assistant
                    </h1>
                    <p className="text-text-secondary my-2">
                        Get personalized career guidance powered by artificial
                        intelligence
                    </p>
                </div>
            </div>

            <div className="flex-1 flex overflow-hidden">
                {/* AI Tools Sidebar */}
                <div className="w-[30rem] bg-white border-r border-border p-6 overflow-y-auto">
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg font-jakarta flex items-center">
                                <Brain className="w-5 h-5 mr-2 text-primary" />
                                AI Tools
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {aiTools.map((tool, index) => (
                                <div
                                    key={index}
                                    className="p-4 border border-border rounded-lg hover:shadow-sm transition-shadow"
                                >
                                    <div className="flex items-center space-x-3 mb-3">
                                        <div
                                            className={`w-10 h-10 rounded-lg flex items-center justify-center ${tool.color}`}
                                        >
                                            <tool.icon className="w-5 h-5 text-white" />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <h3 className="font-medium text-text-primary">
                                                {tool.title}
                                            </h3>
                                        </div>
                                    </div>
                                    <p className="text-sm text-text-secondary mb-3">
                                        {tool.description}
                                    </p>
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        className="w-full"
                                        onClick={() => {
                                            if (
                                                tool.title ===
                                                "Resume Optimizer"
                                            )
                                                handleResumeOptimization();
                                            else if (
                                                tool.title === "Interview Prep"
                                            )
                                                handleStartInterview();
                                            else if (
                                                tool.title === "Career Advisor"
                                            )
                                                handleCareerAdvice();
                                            else if (
                                                tool.title === "Skills Analysis"
                                            )
                                                handleSkillsAnalysis();
                                        }}
                                    >
                                        {tool.action}
                                    </Button>
                                </div>
                            ))}
                        </CardContent>
                    </Card>

                    {/* Quick Questions */}
                    <Card className="mt-6">
                        <CardHeader>
                            <CardTitle className="text-lg font-jakarta">
                                Quick Questions
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                {[
                                    "How can I improve my resume?",
                                    "What skills are in demand?",
                                    "Interview preparation tips?",
                                    "Salary range for my role?",
                                    "Career transition advice?",
                                    "Remote job application tips?",
                                ].map((question, index) => (
                                    <Button
                                        key={index}
                                        variant="outline"
                                        size="sm"
                                        className="w-full text-left justify-start text-xs hover:bg-primary hover:text-white transition-colors"
                                        onClick={() => setMessage(question)}
                                    >
                                        {question}
                                    </Button>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Main Chat Interface */}
                <div className="flex-1 flex flex-col bg-gray-50">
                    {/* Chat Messages */}
                    <div className="flex-1 overflow-y-auto p-6 space-y-4">
                        {chatHistory.map((chat, index) => (
                            <div
                                key={index}
                                className={`flex ${
                                    chat.type === "user"
                                        ? "justify-end"
                                        : "justify-start"
                                }`}
                            >
                                <div
                                    className={`max-w-[70%] flex items-start space-x-3 ${
                                        chat.type === "user"
                                            ? "flex-row-reverse space-x-reverse"
                                            : "flex-row"
                                    }`}
                                >
                                    {/* Avatar */}
                                    <div
                                        className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                                            chat.type === "user"
                                                ? "bg-primary text-white"
                                                : "bg-gray-200 text-gray-600"
                                        }`}
                                    >
                                        {chat.type === "user" ? (
                                            <User className="w-4 h-4" />
                                        ) : (
                                            <Bot className="w-4 h-4" />
                                        )}
                                    </div>

                                    {/* Message Bubble */}
                                    <div
                                        className={`rounded-2xl px-4 py-3 max-w-full ${
                                            chat.type === "user"
                                                ? "bg-primary text-white rounded-tr-md"
                                                : "bg-white text-text-primary border border-border rounded-tl-md shadow-sm"
                                        }`}
                                    >
                                        <p className="text-sm whitespace-pre-line leading-relaxed">
                                            {chat.message}
                                        </p>
                                        <p
                                            className={`text-xs mt-2 ${
                                                chat.type === "user"
                                                    ? "text-white/70"
                                                    : "text-text-muted"
                                            }`}
                                        >
                                            {chat.time}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        ))}

                        {/* Loading Message */}
                        {isLoading && (
                            <div className="flex justify-start">
                                <div className="flex items-start space-x-3">
                                    <div className="w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center">
                                        <Bot className="w-4 h-4" />
                                    </div>
                                    <div className="bg-white text-text-primary border border-border rounded-2xl rounded-tl-md shadow-sm px-4 py-3">
                                        <div className="flex items-center space-x-2">
                                            <Loader className="w-4 h-4 animate-spin text-primary" />
                                            <span className="text-sm text-text-secondary">
                                                AI is thinking...
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        <div ref={messagesEndRef} />
                    </div>

                    {/* Chat Input - Fixed at Bottom */}
                    <div className="bg-white border-t border-border p-4 flex-shrink-0">
                        <div className="flex space-x-3 items-end">
                            <div className="flex-1">
                                <Input
                                    value={message}
                                    onChange={(e) => setMessage(e.target.value)}
                                    placeholder="Ask me about your career, resume, or job search..."
                                    className="w-full resize-none border-2 border-gray-200 focus:border-primary rounded-xl px-4 py-3"
                                    disabled={isLoading}
                                    onKeyPress={(e) => {
                                        if (e.key === "Enter" && !e.shiftKey) {
                                            e.preventDefault();
                                            handleSendMessage();
                                        }
                                    }}
                                />
                            </div>
                            <Button
                                onClick={handleSendMessage}
                                disabled={!message.trim() || isLoading}
                                className="bg-primary hover:bg-primary-hover text-white rounded-xl px-6 py-3 h-12"
                            >
                                {isLoading ? (
                                    <Loader className="w-4 h-4 animate-spin" />
                                ) : (
                                    <Send className="w-4 h-4" />
                                )}
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Interview Practice Dialog */}
            {currentInterview && (
                <Dialog
                    open={!!currentInterview}
                    onOpenChange={() => setCurrentInterview(null)}
                >
                    <DialogContent className="max-w-2xl">
                        <DialogHeader>
                            <DialogTitle>
                                Interview Practice Session
                            </DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4">
                            {currentInterview.questionIndex <
                            interviewQuestions.length ? (
                                <>
                                    <div className="p-4 bg-primary/10 rounded-lg">
                                        <h3 className="font-medium mb-2">
                                            Question{" "}
                                            {currentInterview.questionIndex + 1}
                                            :
                                        </h3>
                                        <p>
                                            {
                                                interviewQuestions[
                                                    currentInterview
                                                        .questionIndex
                                                ]
                                            }
                                        </p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium">
                                            Your Answer:
                                        </label>
                                        <Textarea
                                            value={currentInterview.userAnswer}
                                            onChange={(e) =>
                                                setCurrentInterview((prev) => ({
                                                    ...prev,
                                                    userAnswer: e.target.value,
                                                }))
                                            }
                                            placeholder="Type your answer here..."
                                            rows={4}
                                        />
                                    </div>
                                    <div className="flex space-x-2">
                                        <Button
                                            onClick={handleInterviewAnswer}
                                            className="flex-1"
                                        >
                                            Submit Answer
                                        </Button>
                                        <Button
                                            variant="outline"
                                            onClick={() =>
                                                setCurrentInterview(null)
                                            }
                                        >
                                            End Session
                                        </Button>
                                    </div>
                                </>
                            ) : (
                                <div className="text-center">
                                    <CheckCircle className="w-12 h-12 text-success mx-auto mb-4" />
                                    <h3 className="text-lg font-semibold mb-2">
                                        Interview Practice Complete!
                                    </h3>
                                    <p className="text-text-secondary mb-4">
                                        You've answered all questions. Here's
                                        your feedback:
                                    </p>
                                    <div className="space-y-3 text-left">
                                        {currentInterview.feedback.map(
                                            (item, index) => (
                                                <div
                                                    key={index}
                                                    className="p-3 border rounded-lg"
                                                >
                                                    <p className="font-medium text-sm mb-1">
                                                        Q: {item.question}
                                                    </p>
                                                    <p className="text-sm text-success">
                                                        {item.feedback}
                                                    </p>
                                                </div>
                                            )
                                        )}
                                    </div>
                                    <Button
                                        onClick={() =>
                                            setCurrentInterview(null)
                                        }
                                        className="mt-4"
                                    >
                                        Close
                                    </Button>
                                </div>
                            )}
                        </div>
                    </DialogContent>
                </Dialog>
            )}
        </div>
    );
};

export default AIAssistant;
