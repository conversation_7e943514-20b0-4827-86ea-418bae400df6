import type { Config } from "tailwindcss";

export default {
    darkMode: ["class"],
    content: [
        "./pages/**/*.{ts,tsx}",
        "./components/**/*.{ts,tsx}",
        "./app/**/*.{ts,tsx}",
        "./src/**/*.{ts,tsx}",
    ],
    prefix: "",
    theme: {
        container: {
            center: true,
            padding: "2rem",
            screens: {
                "2xl": "1400px",
            },
        },
        extend: {
            fontFamily: {
                inter: ["Inter", "sans-serif"],
                jakarta: ["Plus Jakarta Sans", "sans-serif"],
                mono: ["JetBrains Mono", "monospace"],
            },
            colors: {
                border: "hsl(var(--border))",
                input: "hsl(var(--input))",
                ring: "hsl(var(--ring))",
                background: "hsl(var(--background))",
                foreground: "hsl(var(--foreground))",
                primary: {
                    DEFAULT: "#3b82f6",
                    foreground: "#ffffff",
                    hover: "#2563eb",
                    light: "#dbeafe",
                },
                secondary: {
                    DEFAULT: "#f8fafc",
                    foreground: "#1e293b",
                },
                destructive: {
                    DEFAULT: "hsl(var(--destructive))",
                    foreground: "hsl(var(--destructive-foreground))",
                },
                muted: {
                    DEFAULT: "hsl(var(--muted))",
                    foreground: "hsl(var(--muted-foreground))",
                },
                accent: {
                    DEFAULT: "hsl(var(--accent))",
                    foreground: "hsl(var(--accent-foreground))",
                },
                popover: {
                    DEFAULT: "hsl(var(--popover))",
                    foreground: "hsl(var(--popover-foreground))",
                },
                card: {
                    DEFAULT: "hsl(var(--card))",
                    foreground: "hsl(var(--card-foreground))",
                },
                success: {
                    DEFAULT: "#10b981",
                    foreground: "#ffffff",
                    light: "#d1fae5",
                    text: "#065f46",
                },
                text: {
                    primary: "#1e293b",
                    secondary: "#64748b",
                    muted: "#94a3b8",
                    placeholder: "#cbd5e1",
                },
                warning: {
                    DEFAULT: "#f59e0b",
                    foreground: "#ffffff",
                    light: "#fef3c7",
                },
                error: {
                    DEFAULT: "#ef4444",
                    foreground: "#ffffff",
                    light: "#fee2e2",
                },
                sidebar: {
                    DEFAULT: "#f1f5f9",
                    foreground: "#1e293b",
                    primary: "#3b82f6",
                    "primary-foreground": "#ffffff",
                    accent: "#e2e8f0",
                    "accent-foreground": "#1e293b",
                    border: "#e2e8f0",
                    ring: "#3b82f6",
                },
                // Custom CareerAlgo brand colors
                "brand-blue": {
                    50: "#eff6ff",
                    100: "#dbeafe",
                    500: "#3b82f6",
                    600: "#2563eb",
                    700: "#1d4ed8",
                },
                "brand-green": {
                    500: "#10b981",
                },
                "brand-orange": {
                    500: "#f59e0b",
                },
                "brand-purple": {
                    500: "#8b5cf6",
                },
            },
            borderRadius: {
                lg: "var(--radius)",
                md: "calc(var(--radius) - 2px)",
                sm: "calc(var(--radius) - 4px)",
            },
            keyframes: {
                "accordion-down": {
                    from: {
                        height: "0",
                    },
                    to: {
                        height: "var(--radix-accordion-content-height)",
                    },
                },
                "accordion-up": {
                    from: {
                        height: "var(--radix-accordion-content-height)",
                    },
                    to: {
                        height: "0",
                    },
                },
                "fade-in": {
                    "0%": {
                        opacity: "0",
                        transform: "translateY(10px)",
                    },
                    "100%": {
                        opacity: "1",
                        transform: "translateY(0)",
                    },
                },
                counter: {
                    "0%": { opacity: "0", transform: "translateY(20px)" },
                    "100%": { opacity: "1", transform: "translateY(0)" },
                },
                "scale-in": {
                    "0%": {
                        transform: "scale(0.95)",
                        opacity: "0",
                    },
                    "100%": {
                        transform: "scale(1)",
                        opacity: "1",
                    },
                },
                "slide-in-right": {
                    "0%": { transform: "translateX(100%)" },
                    "100%": { transform: "translateX(0)" },
                },
            },
            animation: {
                "accordion-down": "accordion-down 0.2s ease-out",
                "accordion-up": "accordion-up 0.2s ease-out",
                "fade-in": "fade-in 0.6s ease-out",
                "scale-in": "scale-in 0.2s ease-out",
                "slide-in-right": "slide-in-right 0.3s ease-out",
                counter: "counter 0.8s ease-out",
            },
        },
    },
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    plugins: [require("tailwindcss-animate")],
} satisfies Config;
