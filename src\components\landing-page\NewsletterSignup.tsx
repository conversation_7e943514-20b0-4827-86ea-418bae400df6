
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Mail, Gift, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const NewsletterSignup = () => {
  const [email, setEmail] = useState('');
  const [isSubscribed, setIsSubscribed] = useState(false);
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubscribed(true);
      toast({
        title: "Successfully subscribed!",
        description: "Check your email for your free career guide.",
      });
    }
  };

  if (isSubscribed) {
    return (
      <section className="py-16 bg-gradient-to-r from-green-50 to-emerald-50 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-jakarta font-bold text-slate-900 mb-4">
            Welcome to the CareerAlgo Community!
          </h2>
          <p className="text-gray-600">
            Check your email for your free "Ultimate Career Transformation Guide" and weekly insights.
          </p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gradient-to-r from-brand-blue-50 to-purple-50 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-blue-100 rounded-full mb-6">
          <Mail className="w-8 h-8 text-brand-blue-500" />
        </div>
        
        <Badge variant="outline" className="mb-4 text-brand-blue-500 border-brand-blue-200 bg-brand-blue-50">
          <Gift size={14} className="mr-1" />
          Free Career Guide Included
        </Badge>
        
        <h2 className="text-3xl font-jakarta font-bold text-slate-900 mb-4">
          Get Weekly Career Insights
        </h2>
        <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
          Join 25,000+ professionals receiving exclusive tips, industry insights, and AI-powered career strategies
        </p>

        <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto mb-6">
          <Input
            type="email"
            placeholder="Enter your email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="flex-1"
            required
          />
          <Button type="submit" className="bg-brand-blue-500 hover:bg-brand-blue-600 text-white px-8">
            Get Free Guide
          </Button>
        </form>

        <div className="flex items-center justify-center gap-6 text-sm text-gray-500">
          <div className="flex items-center gap-1">
            <CheckCircle size={14} className="text-green-500" />
            <span>Free forever</span>
          </div>
          <div className="flex items-center gap-1">
            <CheckCircle size={14} className="text-green-500" />
            <span>Unsubscribe anytime</span>
          </div>
          <div className="flex items-center gap-1">
            <CheckCircle size={14} className="text-green-500" />
            <span>No spam guarantee</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewsletterSignup;
