
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, Award, Target, Globe } from "lucide-react";

const AboutSection = () => {
  return (
    <section id="about" className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-blue-50/30"></div>
      
      <div className="max-w-7xl mx-auto relative">
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4 text-brand-blue-500 border-brand-blue-200 bg-brand-blue-50/80">
            Our Story
          </Badge>
          <h2 className="text-3xl md:text-4xl font-jakarta font-bold text-slate-900 mb-4">
            Revolutionizing Career Development with <span className="text-transparent bg-gradient-to-r from-brand-blue-500 to-purple-600 bg-clip-text">AI Innovation</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Founded by career experts and AI researchers, we're on a mission to democratize career success through intelligent technology
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 items-center mb-16">
          <div>
            <h3 className="text-2xl font-jakarta font-bold text-slate-900 mb-6">Our Mission</h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              We believe everyone deserves access to career opportunities that match their potential. Our AI-powered platform eliminates barriers and accelerates career growth for professionals worldwide.
            </p>
            <div className="space-y-4">
              {[
                { icon: Target, text: "AI-driven career matching with 95% accuracy" },
                { icon: Users, text: "Supporting 50,000+ professionals globally" },
                { icon: Award, text: "Industry-leading success rates" }
              ].map((item, index) => (
                <div key={index} className="flex items-center gap-3">
                  <item.icon className="w-5 h-5 text-brand-blue-500" />
                  <span className="text-gray-700">{item.text}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            {[
              { number: "2019", label: "Founded" },
              { number: "50K+", label: "Users Served" },
              { number: "500+", label: "Partner Companies" },
              { number: "98%", label: "Satisfaction Rate" }
            ].map((stat, index) => (
              <Card key={index} className="p-6 text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-0">
                  <div className="text-2xl font-bold text-brand-blue-500 mb-1">{stat.number}</div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Team Section */}
        <div className="text-center">
          <h3 className="text-2xl font-jakarta font-bold text-slate-900 mb-8">Leadership Team</h3>
          <div className="grid md:grid-cols-3 gap-8">
            {[
              { name: "Sarah Johnson", role: "CEO & Co-Founder", description: "Former VP at LinkedIn, 15+ years in career development", avatar: "👩‍💼" },
              { name: "David Chen", role: "CTO & Co-Founder", description: "AI researcher from Stanford, ex-Google ML engineer", avatar: "👨‍💻" },
              { name: "Maria Rodriguez", role: "Head of Product", description: "Product leader from Netflix, UX design expert", avatar: "👩‍🎨" }
            ].map((member, index) => (
              <Card key={index} className="p-6 hover:shadow-lg transition-shadow">
                <CardContent className="p-0 text-center">
                  <div className="text-4xl mb-4">{member.avatar}</div>
                  <h4 className="text-lg font-semibold text-slate-900 mb-1">{member.name}</h4>
                  <p className="text-brand-blue-500 font-medium mb-2">{member.role}</p>
                  <p className="text-sm text-gray-600">{member.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
