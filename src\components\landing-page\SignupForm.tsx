
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Check } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface SignupFormProps {
  isOpen: boolean;
  onClose: () => void;
}

const SignupForm = ({ isOpen, onClose }: SignupFormProps) => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    toast({
      title: "Welcome to CareerAlgo!",
      description: "Check your email to get started with your free account.",
    });
    
    setIsSubmitting(false);
    setEmail('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>Start Your Free Trial</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-blue-500 focus:border-brand-blue-500"
              placeholder="<EMAIL>"
            />
          </div>
          
          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <Check size={16} className="text-brand-green-500" />
              <span>No credit card required</span>
            </div>
            <div className="flex items-center gap-2">
              <Check size={16} className="text-brand-green-500" />
              <span>Cancel anytime</span>
            </div>
            <div className="flex items-center gap-2">
              <Check size={16} className="text-brand-green-500" />
              <span>30-day money-back guarantee</span>
            </div>
          </div>

          <Button 
            type="submit" 
            className="w-full bg-brand-blue-500 hover:bg-brand-blue-600"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Creating Account...' : 'Start Free Trial'}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default SignupForm;
