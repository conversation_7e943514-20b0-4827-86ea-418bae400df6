import React from "react";
import { Outlet } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import DashboardSidebar from "./DashboardSidebar";
import DashboardTopBar from "./DashboardTopBar";

const DashboardLayout = () => {
    return (
        <SidebarProvider>
            <div className="min-h-screen flex w-full bg-secondary/30">
                <DashboardSidebar />

                <div className="flex-1 flex flex-col">
                    <DashboardTopBar />

                    <main className="flex-1 p-6 pt-22">
                        <Outlet />
                    </main>
                </div>
            </div>
        </SidebarProvider>
    );
};

export default DashboardLayout;
