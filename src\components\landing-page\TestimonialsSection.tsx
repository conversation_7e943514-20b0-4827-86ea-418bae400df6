
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Star } from "lucide-react";

const TestimonialsSection = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Software Engineer at Google",
      content: "<PERSON><PERSON><PERSON><PERSON> helped me land a $120k role at Google in just 4 weeks. The AI resume enhancement was incredible!",
      avatar: "👩‍💻"
    },
    {
      name: "<PERSON>",
      role: "Product Manager at Netflix",
      content: "From unemployed to Product Manager at Netflix. This platform changed my life completely.",
      avatar: "👨‍💼"
    },
    {
      name: "<PERSON><PERSON>",
      role: "Data Scientist at Microsoft",
      content: "The interview prep feature gave me confidence I never had. Landed 3 offers in one month!",
      avatar: "👩‍🔬"
    }
  ];

  return (
    <section className="py-20 bg-gray-50 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-jakarta font-bold text-slate-900 mb-4">
            Success Stories from Real Users
          </h2>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="p-6 bg-white hover:shadow-lg transition-shadow">
              <CardContent className="p-0">
                <div className="flex text-yellow-400 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} size={16} fill="currentColor" />
                  ))}
                </div>
                <p className="text-gray-700 mb-4 italic">"{testimonial.content}"</p>
                <div className="flex items-center gap-3">
                  <div className="text-3xl">{testimonial.avatar}</div>
                  <div>
                    <h4 className="font-semibold text-slate-900">{testimonial.name}</h4>
                    <p className="text-sm text-gray-600">{testimonial.role}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
