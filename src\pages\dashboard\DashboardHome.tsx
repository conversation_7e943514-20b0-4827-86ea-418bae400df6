import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
    TrendingUp,
    FileText,
    Target,
    Eye,
    CheckCircle,
    Clock,
    Star,
    ArrowUpRight,
    Upload,
    Search,
    Brain,
    BarChart3,
} from "lucide-react";
import { useUser } from "@clerk/clerk-react";

const DashboardHome = () => {
    const { user } = useUser();

    const kpiCards = [
        {
            title: "Active Applications",
            value: "12",
            change: "+3 this week",
            changeType: "positive",
            icon: FileText,
            action: "View All",
        },
        {
            title: "Interview Success Rate",
            value: "78%",
            change: "Above average",
            changeType: "positive",
            icon: Target,
            action: "See Details",
        },
        {
            title: "Profile Strength",
            value: `${user?.profileStrength || 87}/100`,
            change: "+5 this month",
            changeType: "positive",
            icon: TrendingUp,
            action: "Improve Score",
        },
        {
            title: "Jobs Viewed",
            value: "156",
            change: "This month",
            changeType: "neutral",
            icon: Eye,
            action: "Find More",
        },
    ];

    const quickActions = [
        {
            title: "Upload New Resume",
            description: "Add or update your resume for AI analysis",
            icon: Upload,
            color: "bg-primary",
            action: "Upload",
        },
        {
            title: "Find Jobs Now",
            description: "Discover personalized job recommendations",
            icon: Search,
            color: "bg-success",
            action: "Search",
        },
        {
            title: "AI Enhancement",
            description: "Optimize your resume with AI suggestions",
            icon: Brain,
            color: "bg-warning",
            action: "Enhance",
        },
        {
            title: "View Analytics",
            description: "Check your career progress and insights",
            icon: BarChart3,
            color: "bg-error",
            action: "View",
        },
    ];

    const recentActivity = [
        {
            type: "application",
            title: "Applied to Senior Developer at TechCorp",
            time: "2 hours ago",
            status: "submitted",
            icon: FileText,
        },
        {
            type: "resume",
            title: "Resume analyzed and optimized",
            time: "1 day ago",
            status: "completed",
            icon: CheckCircle,
        },
        {
            type: "interview",
            title: "Interview scheduled with DataCorp",
            time: "2 days ago",
            status: "scheduled",
            icon: Clock,
        },
        {
            type: "job",
            title: "New job recommendations available",
            time: "3 days ago",
            status: "new",
            icon: Star,
        },
    ];

    const jobRecommendations = [
        {
            title: "Senior Software Engineer",
            company: "TechFlow Inc.",
            location: "San Francisco, CA",
            salary: "$120k - $150k",
            match: 95,
            logo: null,
        },
        {
            title: "Product Manager",
            company: "InnovateLabs",
            location: "Remote",
            salary: "$110k - $140k",
            match: 88,
            logo: null,
        },
        {
            title: "Full Stack Developer",
            company: "StartupXYZ",
            location: "New York, NY",
            salary: "$100k - $130k",
            match: 82,
            logo: null,
        },
    ];

    return (
        <div className="space-y-6">
            {/* Welcome Section */}
            <div className="bg-gradient-to-r from-primary to-primary-hover rounded-xl p-6 text-white">
                <h1 className="text-2xl font-jakarta font-bold mb-2">
                    Welcome back, {user?.fullName?.split(" ")[0]}! 👋
                </h1>
                <p className="text-primary-light mb-4">
                    You have 3 new job matches and 2 interview invitations
                    waiting for you.
                </p>
                <div className="flex space-x-3">
                    <Button variant="secondary" size="sm">
                        View Jobs
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        className="border-white/20 text-black hover:bg-white/10"
                    >
                        Schedule Interviews
                    </Button>
                </div>
            </div>

            {/* KPI Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {kpiCards.map((kpi, index) => (
                    <Card
                        key={index}
                        className="hover:shadow-md transition-shadow"
                    >
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between mb-4">
                                <div
                                    className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                                        kpi.changeType === "positive"
                                            ? "bg-success/10"
                                            : "bg-primary/10"
                                    }`}
                                >
                                    <kpi.icon
                                        className={`w-6 h-6 ${
                                            kpi.changeType === "positive"
                                                ? "text-success"
                                                : "text-primary"
                                        }`}
                                    />
                                </div>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-primary hover:text-primary-hover"
                                >
                                    {kpi.action}
                                    <ArrowUpRight className="w-4 h-4 ml-1" />
                                </Button>
                            </div>

                            <div className="space-y-1">
                                <h3 className="text-2xl font-jakarta font-bold text-text-primary">
                                    {kpi.value}
                                </h3>
                                <p className="text-sm text-text-secondary">
                                    {kpi.title}
                                </p>
                                <p
                                    className={`text-xs font-medium ${
                                        kpi.changeType === "positive"
                                            ? "text-success"
                                            : "text-text-muted"
                                    }`}
                                >
                                    {kpi.change}
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Quick Actions */}
                <div className="lg:col-span-1">
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg font-jakarta">
                                Quick Actions
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {quickActions.map((action, index) => (
                                <div
                                    key={index}
                                    className="flex items-center space-x-4 p-3 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
                                >
                                    <div
                                        className={`w-10 h-10 rounded-lg flex items-center justify-center ${action.color}`}
                                    >
                                        <action.icon className="w-5 h-5 text-white" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h4 className="font-medium text-text-primary">
                                            {action.title}
                                        </h4>
                                        <p className="text-xs text-text-secondary">
                                            {action.description}
                                        </p>
                                    </div>
                                    <Button size="sm" variant="ghost">
                                        {action.action}
                                    </Button>
                                </div>
                            ))}
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Activity */}
                <div className="lg:col-span-2">
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg font-jakarta">
                                Recent Activity
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentActivity.map((activity, index) => (
                                    <div
                                        key={index}
                                        className="flex items-start space-x-4 p-3 rounded-lg hover:bg-muted/30 transition-colors"
                                    >
                                        <div
                                            className={`w-8 h-8 rounded-full flex items-center justify-center mt-1 ${
                                                activity.status === "completed"
                                                    ? "bg-success/10"
                                                    : activity.status ===
                                                      "scheduled"
                                                    ? "bg-warning/10"
                                                    : activity.status === "new"
                                                    ? "bg-primary/10"
                                                    : "bg-muted"
                                            }`}
                                        >
                                            <activity.icon
                                                className={`w-4 h-4 ${
                                                    activity.status ===
                                                    "completed"
                                                        ? "text-success"
                                                        : activity.status ===
                                                          "scheduled"
                                                        ? "text-warning"
                                                        : activity.status ===
                                                          "new"
                                                        ? "text-primary"
                                                        : "text-text-muted"
                                                }`}
                                            />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <h4 className="font-medium text-text-primary">
                                                {activity.title}
                                            </h4>
                                            <p className="text-sm text-text-secondary">
                                                {activity.time}
                                            </p>
                                        </div>
                                        <div
                                            className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                activity.status === "completed"
                                                    ? "bg-success/10 text-success"
                                                    : activity.status ===
                                                      "scheduled"
                                                    ? "bg-warning/10 text-warning"
                                                    : activity.status === "new"
                                                    ? "bg-primary/10 text-primary"
                                                    : "bg-muted text-text-muted"
                                            }`}
                                        >
                                            {activity.status}
                                        </div>
                                    </div>
                                ))}
                            </div>

                            <div className="mt-4 pt-4 border-t border-border">
                                <Button
                                    variant="ghost"
                                    className="w-full text-primary hover:text-primary-hover"
                                >
                                    View All Activity
                                    <ArrowUpRight className="w-4 h-4 ml-1" />
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>

            {/* Job Recommendations */}
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle className="text-lg font-jakarta">
                        Recommended Jobs
                    </CardTitle>
                    <Button variant="outline" size="sm">
                        View All Jobs
                    </Button>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {jobRecommendations.map((job, index) => (
                            <div
                                key={index}
                                className="border border-border rounded-lg p-4 hover:shadow-md transition-shadow"
                            >
                                <div className="flex items-start justify-between mb-3">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                                            <span className="text-xs font-medium text-primary">
                                                {job.company.charAt(0)}
                                            </span>
                                        </div>
                                        <div>
                                            <h4 className="font-medium text-text-primary">
                                                {job.title}
                                            </h4>
                                            <p className="text-sm text-text-secondary">
                                                {job.company}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                        <Star className="w-4 h-4 text-warning fill-current" />
                                        <span className="text-sm font-medium text-text-primary">
                                            {job.match}%
                                        </span>
                                    </div>
                                </div>

                                <div className="space-y-2 mb-4">
                                    <p className="text-sm text-text-secondary">
                                        {job.location}
                                    </p>
                                    <p className="text-sm font-medium text-success">
                                        {job.salary}
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <div className="flex items-center justify-between text-xs">
                                        <span className="text-text-secondary">
                                            Match Score
                                        </span>
                                        <span className="text-text-primary font-medium">
                                            {job.match}%
                                        </span>
                                    </div>
                                    <Progress
                                        value={job.match}
                                        className="h-1"
                                    />
                                </div>

                                <div className="flex space-x-2 mt-4">
                                    <Button
                                        size="sm"
                                        className="flex-1 bg-primary hover:bg-primary-hover text-white"
                                    >
                                        Apply Now
                                    </Button>
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        className="flex-1"
                                    >
                                        Save Job
                                    </Button>
                                </div>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};

export default DashboardHome;
