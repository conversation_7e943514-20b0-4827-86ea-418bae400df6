
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";

const PricingSection = () => {
  const plans = [
    {
      name: "Free",
      subtitle: "Get Started",
      price: "$0",
      period: "/month",
      features: [
        "3 resume enhancements",
        "10 job applications",
        "Basic interview prep",
        "Community support"
      ],
      cta: "Start Free",
      popular: false
    },
    {
      name: "Pro",
      subtitle: "Most Popular",
      price: "$29",
      period: "/month",
      features: [
        "Unlimited resume enhancements",
        "Unlimited job applications",
        "Advanced AI features",
        "Priority support",
        "Career analytics dashboard"
      ],
      cta: "Upgrade to Pro",
      popular: true
    },
    {
      name: "Enterprise",
      subtitle: "For Teams",
      price: "Custom",
      period: "",
      features: [
        "Everything in Pro",
        "Team management",
        "Custom integrations",
        "Dedicated support",
        "Advanced reporting"
      ],
      cta: "Contact Sales",
      popular: false
    }
  ];

  return (
    <section id="pricing" className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-jakarta font-bold text-slate-900 mb-4">
            Choose Your Career Acceleration Plan
          </h2>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {plans.map((plan, index) => (
            <Card key={index} className={`p-6 relative ${plan.popular ? 'border-brand-blue-500 border-2 shadow-xl scale-105' : 'border-gray-200'}`}>
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-brand-blue-500 text-white">
                  Most Popular
                </Badge>
              )}
              <CardContent className="p-0">
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-jakarta font-bold text-slate-900">{plan.name}</h3>
                  <p className="text-gray-600 mb-4">{plan.subtitle}</p>
                  <div className="flex items-baseline justify-center">
                    <span className="text-4xl font-bold text-slate-900">{plan.price}</span>
                    <span className="text-gray-600 ml-1">{plan.period}</span>
                  </div>
                </div>
                
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center gap-2">
                      <Check size={16} className="text-brand-green-500" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button className={`w-full ${plan.popular ? 'bg-brand-blue-500 hover:bg-brand-blue-600 text-white' : 'border-brand-blue-500 text-brand-blue-500 hover:bg-brand-blue-50'}`} variant={plan.popular ? 'default' : 'outline'}>
                  {plan.cta}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
