// API endpoints mapping
export const API_ENDPOINTS = {
    // Authentication
    auth: {
        validateToken: '/auth/validate-token',
        publicKey: '/auth/public-key',
    },

    // User Management
    users: {
        profile: '/users/me',
        stats: '/users/me/stats',
        avatar: '/users/me/avatar',
        deactivate: '/users/me/deactivate',
        exportData: '/users/me/export-data',
    },

    // Resume Management
    resumes: {
        list: '/resumes',
        create: '/resumes',
        get: (id: string) => `/resumes/${id}`,
        update: (id: string) => `/resumes/${id}`,
        delete: (id: string) => `/resumes/${id}`,
        setPrimary: (id: string) => `/resumes/${id}/set-primary`,
        duplicate: (id: string) => `/resumes/${id}/duplicate`,
        share: (id: string) => `/resumes/${id}/share`,
        download: (id: string) => `/resumes/${id}/download`,
        search: '/resumes/search',
    },

    // Job Management
    jobs: {
        search: '/jobs/search',
        get: (id: string) => `/jobs/${id}`,
        saved: '/jobs/saved',
        save: (id: string) => `/jobs/${id}/save`,
        unsave: (id: string) => `/jobs/${id}/unsave`,
        recommendations: '/jobs/recommendations',
        trending: '/jobs/trending',
        filters: '/jobs/filters',
    },

    // Application Management
    applications: {
        list: '/applications',
        create: '/applications',
        get: (id: string) => `/applications/${id}`,
        update: (id: string) => `/applications/${id}`,
        delete: (id: string) => `/applications/${id}`,
        timeline: (id: string) => `/applications/${id}/timeline`,
        notes: (id: string) => `/applications/${id}/notes`,
        documents: (id: string) => `/applications/${id}/documents`,
        stats: '/applications/stats',
    },

    // Skills Management
    skills: {
        search: '/skills',
        trending: '/skills/trending',
        categories: '/skills/categories',
        get: (id: string) => `/skills/${id}`,
        user: '/skills/user',
        addToUser: '/skills/user',
        updateUser: (id: string) => `/skills/user/${id}`,
        removeFromUser: (id: string) => `/skills/user/${id}`,
        gapAnalysis: '/skills/gap-analysis',
        recommendations: '/skills/recommendations',
        assessment: '/skills/assessment',
        marketData: '/skills/market-data',
    },

    // Interview Preparation
    interviewPrep: {
        sessions: '/interview-prep/sessions',
        getSession: (id: string) => `/interview-prep/sessions/${id}`,
        createSession: '/interview-prep/sessions',
        updateSession: (id: string) => `/interview-prep/sessions/${id}`,
        deleteSession: (id: string) => `/interview-prep/sessions/${id}`,
        answer: (id: string) => `/interview-prep/sessions/${id}/answer`,
        complete: (id: string) => `/interview-prep/sessions/${id}/complete`,
        analytics: '/interview-prep/analytics',
        questions: '/interview-prep/questions',
        companyResearch: (jobId: string) => `/interview-prep/company-research/${jobId}`,
        sessionTypes: '/interview-prep/session-types',
        tips: '/interview-prep/tips',
        recommendations: '/interview-prep/recommendations',
    },

    // AI Features
    ai: {
        resumeAnalysis: '/ai/agents/resume/analyze',
        jobMatching: '/ai/agents/job/match',
        interviewEvaluation: '/ai/agents/interview/evaluate',
        careerAdvice: '/ai/agents/career/advice',
        comprehensiveAnalysis: '/ai/agents/comprehensive/analyze',
        streamAnalysis: (sessionId: string) => `/ai/agents/resume/stream/${sessionId}`,
    },

    // Analytics
    analytics: {
        dashboard: '/analytics/dashboard',
        applications: '/analytics/applications',
        skills: '/analytics/skills',
        marketData: '/analytics/market-data',
        jobSearch: '/analytics/job-search',
        interviewPrep: '/analytics/interview-prep',
        export: '/analytics/export',
    },

    // Subscription Management
    subscription: {
        current: '/subscription/current',
        plans: '/subscription/plans',
        upgrade: '/subscription/upgrade',
        cancel: '/subscription/cancel',
        history: '/subscription/history',
        usage: '/subscription/usage',
        statuses: '/subscription/statuses',
        validate: '/subscription/validate',
    },

    // File Management
    files: {
        upload: '/files/upload',
        delete: (id: string) => `/files/${id}`,
        get: (id: string) => `/files/${id}`,
    },

    // Public Access
    public: {
        resumeShare: (token: string) => `/public/resume/share/${token}`,
        health: '/public/health',
    },

    // System
    system: {
        health: '/health',
        version: '/version',
        status: '/status',
    },
} as const;
