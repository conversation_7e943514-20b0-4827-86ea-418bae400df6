import React from "react";
import { Bell, Search, Menu } from "lucide-react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { UserButton } from "@clerk/clerk-react";
import { SidebarTrigger } from "@/components/ui/sidebar";

const DashboardTopBar = () => {
    return (
        <header className="bg-white/95 backdrop-blur-sm border-b border-border px-6 py-4 sticky top-0 z-50">
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    <SidebarTrigger className="hover:bg-gray-100 p-2 rounded-lg transition-colors" />
                    <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-muted w-4 h-4" />
                        <Input
                            placeholder="Search jobs, resumes, or ask AI..."
                            className="pl-10 w-80 bg-gray-50/50 border-gray-200 focus:bg-white focus:border-primary transition-colors"
                        />
                    </div>
                </div>

                <div className="flex items-center space-x-4">
                    <Button
                        variant="ghost"
                        size="icon"
                        className="relative hover:bg-gray-100 rounded-full"
                    >
                        <Bell className="w-5 h-5" />
                        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">
                            3
                        </span>
                    </Button>

                    <div className="h-8 w-px bg-gray-200"></div>

                    <UserButton
                        appearance={{
                            elements: {
                                avatarBox:
                                    "w-10 h-10 rounded-full border-2 border-white shadow-md hover:shadow-lg transition-shadow",
                                userButtonPopoverCard:
                                    "shadow-xl border border-gray-200",
                                userButtonPopoverActions: "space-y-1",
                            },
                        }}
                    />
                </div>
            </div>
        </header>
    );
};

export default DashboardTopBar;
