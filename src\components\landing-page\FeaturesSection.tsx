
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Target, MessageCircle, BarChart3, PuzzleIcon, Linkedin } from "lucide-react";

interface FeaturesSectionProps {
  onCTAClick: (action: string) => void;
}

const FeaturesSection = ({ onCTAClick }: FeaturesSectionProps) => {
  const features = [
    {
      icon: FileText,
      title: "AI Resume Builder",
      description: "Create professional resumes in minutes with AI-powered optimization and industry-specific templates",
      cta: "Try Resume Builder"
    },
    {
      icon: Target,
      title: "Smart Job Matching",
      description: "Get matched with 95%+ compatible jobs based on your skills, experience, and career goals",
      cta: "Find My Jobs"
    },
    {
      icon: MessageCircle,
      title: "Interview Mastery",
      description: "Practice with AI-powered mock interviews and get personalized feedback to boost confidence",
      cta: "Start Practice"
    },
    {
      icon: BarChart3,
      title: "Career Analytics",
      description: "Monitor your job search with detailed analytics, application tracking, and market insights",
      cta: "View Analytics"
    },
    {
      icon: PuzzleIcon,
      title: "Skills Development",
      description: "Identify missing skills and get personalized learning recommendations to advance your career",
      cta: "Analyze Skills"
    },
    {
      icon: Linkedin,
      title: "LinkedIn Integration",
      description: "Import your LinkedIn profile data and keep everything synchronized across platforms",
      cta: "Connect Profile"
    }
  ];

  return (
    <section id="features" className="py-20 bg-gray-50 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-jakarta font-bold text-slate-900 mb-4">
            Everything You Need to Accelerate Your Career
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive AI-powered tools designed to transform every aspect of your job search
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-white group">
              <CardContent className="p-0">
                <div className="flex justify-center mb-4">
                  <feature.icon className="w-12 h-12 text-brand-blue-500 group-hover:scale-110 transition-transform" />
                </div>
                <h3 className="text-xl font-jakarta font-semibold text-slate-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600 mb-4 leading-relaxed">{feature.description}</p>
                <Button 
                  variant="outline" 
                  onClick={() => onCTAClick(feature.title.toLowerCase())}
                  className="text-brand-blue-500 border-brand-blue-500 hover:bg-brand-blue-50 w-full"
                >
                  {feature.cta}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
