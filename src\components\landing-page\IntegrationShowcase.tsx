
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Linkedin, Github, Mail, Calendar, FileText, Database } from "lucide-react";

const IntegrationShowcase = () => {
  const integrations = [
    {
      name: "LinkedIn",
      icon: Linkedin,
      description: "Import profile data and sync job applications",
      category: "Social",
      color: "bg-blue-500"
    },
    {
      name: "GitHub",
      icon: Github,
      description: "Showcase your coding projects and contributions",
      category: "Development",
      color: "bg-gray-800"
    },
    {
      name: "Google Calendar",
      icon: Calendar,
      description: "Schedule interviews and track application deadlines",
      category: "Productivity",
      color: "bg-red-500"
    },
    {
      name: "Gmail",
      icon: Mail,
      description: "Track email communications with recruiters",
      category: "Communication",
      color: "bg-red-400"
    },
    {
      name: "ATS Systems",
      icon: Database,
      description: "Compatible with 95% of applicant tracking systems",
      category: "Enterprise",
      color: "bg-purple-500"
    },
    {
      name: "PDF Export",
      icon: FileText,
      description: "Export optimized resumes in multiple formats",
      category: "Documents",
      color: "bg-green-500"
    }
  ];

  return (
    <section className="py-20 bg-gray-50 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4 text-brand-blue-500 border-brand-blue-200 bg-brand-blue-50">
            Seamless Integrations
          </Badge>
          <h2 className="text-3xl md:text-4xl font-jakarta font-bold text-slate-900 mb-4">
            Connect Your <span className="text-transparent bg-gradient-to-r from-brand-blue-500 to-purple-600 bg-clip-text">Favorite Tools</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Import data from platforms you already use and streamline your entire job search workflow
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {integrations.map((integration, index) => (
            <Card key={index} className="p-6 hover:shadow-lg transition-all duration-300 group">
              <CardContent className="p-0">
                <div className="flex items-start gap-4">
                  <div className={`p-3 rounded-lg ${integration.color} text-white group-hover:scale-110 transition-transform`}>
                    <integration.icon size={24} />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-semibold text-slate-900">{integration.name}</h3>
                      <Badge variant="outline" className="text-xs">{integration.category}</Badge>
                    </div>
                    <p className="text-gray-600 text-sm">{integration.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <div className="inline-flex items-center gap-4 p-6 bg-white rounded-2xl shadow-lg">
            <div className="text-left">
              <h3 className="text-lg font-semibold text-slate-900 mb-1">Need a Custom Integration?</h3>
              <p className="text-gray-600 text-sm">We can build custom connections for enterprise clients</p>
            </div>
            <Button className="bg-brand-blue-500 hover:bg-brand-blue-600 text-white">
              Contact Sales
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default IntegrationShowcase;
