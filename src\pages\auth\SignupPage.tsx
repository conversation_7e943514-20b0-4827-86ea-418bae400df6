import React from "react";
import { Link } from "react-router-dom";
import { SignUp } from "@clerk/clerk-react";
import { Briefcase, CheckCircle } from "lucide-react";

const SignupPage = () => {
    return (
        <div className="min-h-screen flex">
            {/* Left Side - Signup Form */}
            <div className="flex-1 flex items-center justify-center p-8 bg-white">
                <div className="w-full max-w-md space-y-8">
                    {/* Logo and Header */}
                    <div className="text-center mb-8">
                        <div className="flex items-center justify-center mb-6">
                            <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center">
                                <Briefcase className="w-6 h-6 text-white" />
                            </div>
                            <span className="ml-3 text-2xl font-jakarta font-bold text-text-primary">
                                CareerAlgo
                            </span>
                        </div>
                        <h1 className="text-3xl font-jakarta font-bold text-text-primary">
                            Create your account
                        </h1>
                        <p className="mt-2 text-text-secondary">
                            Start your AI-powered career journey today
                        </p>
                    </div>

                    {/* Clerk Sign Up Component */}
                    <div className="flex justify-center">
                        <SignUp
                            fallbackRedirectUrl="/onboarding"
                            signInUrl="/login"
                            appearance={{
                                elements: {
                                    rootBox: "mx-auto",
                                    card: "shadow-none border-0",
                                },
                            }}
                        />
                    </div>

                    <p className="text-center text-text-secondary">
                        Already have an account?{" "}
                        <Link
                            to="/login"
                            className="text-primary hover:text-primary-hover font-medium"
                        >
                            Sign in
                        </Link>
                    </p>
                </div>
            </div>

            {/* Right Side - Visual Content */}
            <div className="hidden lg:flex flex-1 bg-gradient-to-br from-success/5 to-primary/10 items-center justify-center p-8">
                <div className="max-w-md text-center">
                    <div className="mb-8">
                        <div className="bg-white rounded-xl p-6 shadow-sm mb-6">
                            <div className="flex items-center justify-between mb-4">
                                <div className="text-sm font-medium text-text-primary">
                                    Profile Completion
                                </div>
                                <div className="text-sm text-primary font-medium">
                                    75%
                                </div>
                            </div>
                            <div className="w-full bg-muted rounded-full h-2">
                                <div
                                    className="bg-primary h-2 rounded-full"
                                    style={{ width: "75%" }}
                                ></div>
                            </div>
                        </div>
                    </div>

                    <h2 className="text-2xl font-jakarta font-bold text-text-primary mb-4">
                        Join 50,000+ Professionals
                    </h2>
                    <p className="text-text-secondary leading-relaxed mb-6">
                        Get access to AI-powered career tools, personalized job
                        recommendations, and expert guidance to accelerate your
                        professional growth.
                    </p>

                    <div className="space-y-3 text-left">
                        <div className="flex items-center text-sm text-text-secondary">
                            <CheckCircle className="w-4 h-4 text-success mr-3" />
                            <span>AI-powered resume optimization</span>
                        </div>
                        <div className="flex items-center text-sm text-text-secondary">
                            <CheckCircle className="w-4 h-4 text-success mr-3" />
                            <span>Personalized job matching</span>
                        </div>
                        <div className="flex items-center text-sm text-text-secondary">
                            <CheckCircle className="w-4 h-4 text-success mr-3" />
                            <span>Interview preparation tools</span>
                        </div>
                        <div className="flex items-center text-sm text-text-secondary">
                            <CheckCircle className="w-4 h-4 text-success mr-3" />
                            <span>Career analytics & insights</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SignupPage;
