
import React, { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FileText, Target, TrendingUp, Users, Clock, Star } from "lucide-react";

const KeyMetricsSection = () => {
  const AnimatedCounter = ({ target, suffix = "" }: { target: number; suffix?: string }) => {
    const [count, setCount] = useState(0);

    useEffect(() => {
      const increment = target / 100;
      const timer = setInterval(() => {
        setCount(prev => {
          if (prev < target) {
            return Math.min(prev + increment, target);
          }
          clearInterval(timer);
          return target;
        });
      }, 20);
      return () => clearInterval(timer);
    }, [target]);

    return <span>{Math.floor(count).toLocaleString()}{suffix}</span>;
  };

  const mainStats = [
    { 
      number: 50000, 
      suffix: "+", 
      label: "Resumes Enhanced", 
      icon: FileText,
      description: "AI-optimized resumes",
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50"
    },
    { 
      number: 78, 
      suffix: "%", 
      label: "Interview Success Rate", 
      icon: Target,
      description: "Above industry average",
      color: "from-green-500 to-emerald-600",
      bgColor: "bg-green-50"
    },
    { 
      number: 3, 
      suffix: "x", 
      label: "Faster Job Placement", 
      icon: TrendingUp,
      description: "Compared to traditional methods",
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50"
    },
    { 
      number: 500, 
      suffix: "+", 
      label: "Partner Companies", 
      icon: Users,
      description: "Fortune 500 & startups",
      color: "from-orange-500 to-red-500",
      bgColor: "bg-orange-50"
    }
  ];

  const insights = [
    { icon: Clock, label: "Avg. Response Time", value: "< 24hrs", desc: "From application to interview" },
    { icon: TrendingUp, label: "Salary Increase", value: "32%", desc: "Average improvement" },
    { icon: Star, label: "Client Satisfaction", value: "4.9/5", desc: "Based on 10k+ reviews" }
  ];

  return (
    <section className="py-24 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-purple-50/30"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-3xl"></div>
      
      <div className="max-w-7xl mx-auto relative">
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4 text-brand-blue-500 border-brand-blue-200 bg-brand-blue-50/80 backdrop-blur-sm">
            Real Impact, Real Results
          </Badge>
          <h2 className="text-3xl md:text-4xl font-jakarta font-bold text-slate-900 mb-4">
            Transforming Careers with <span className="text-transparent bg-gradient-to-r from-brand-blue-500 to-purple-600 bg-clip-text">AI Precision</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Join thousands of professionals who've accelerated their career journey with data-driven insights
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {mainStats.map((stat, index) => (
            <Card key={index} className="group relative overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white/70 backdrop-blur-sm">
              <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>
              <CardContent className="p-8 relative">
                <div className="flex items-center justify-between mb-6">
                  <div className={`p-3 rounded-2xl ${stat.bgColor} group-hover:scale-110 transition-transform duration-300`}>
                    <stat.icon className={`w-6 h-6 bg-gradient-to-br ${stat.color} bg-clip-text text-transparent`} />
                  </div>
                  <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                </div>
                
                <div className="space-y-3">
                  <div className={`text-4xl md:text-5xl font-jakarta font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent group-hover:scale-105 transition-transform duration-300`}>
                    <AnimatedCounter target={stat.number} suffix={stat.suffix} />
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 group-hover:text-slate-700 transition-colors">
                    {stat.label}
                  </h3>
                  <p className="text-sm text-gray-600 leading-relaxed">
                    {stat.description}
                  </p>
                </div>

                {/* Micro-interaction indicator */}
                <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="w-1 h-8 bg-gradient-to-t from-transparent to-current opacity-20 rounded-full"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional insights row */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6">
          {insights.map((insight, index) => (
            <div key={index} className="text-center p-6 rounded-2xl bg-white/50 backdrop-blur-sm border border-gray-100 hover:bg-white/70 transition-all duration-300 group">
              <insight.icon className="w-8 h-8 mx-auto mb-3 text-brand-blue-500 group-hover:scale-110 transition-transform" />
              <div className="text-2xl font-bold text-slate-900 mb-1">{insight.value}</div>
              <div className="text-sm font-medium text-slate-700 mb-1">{insight.label}</div>
              <div className="text-xs text-gray-500">{insight.desc}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default KeyMetricsSection;
