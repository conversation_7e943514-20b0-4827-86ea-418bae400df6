import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { userApi, UserStats } from '@/lib/api/services/userApi';
import { User } from '@/lib/types/api';
import { toast } from 'sonner';

// Query Keys
export const userQueryKeys = {
    all: ['users'] as const,
    profile: () => [...userQueryKeys.all, 'profile'] as const,
    stats: () => [...userQueryKeys.all, 'stats'] as const,
    achievements: () => [...userQueryKeys.all, 'achievements'] as const,
    activity: (params?: any) => [...userQueryKeys.all, 'activity', params] as const,
};

// User Profile Hooks
export const useUserProfile = () => {
    return useQuery({
        queryKey: userQueryKeys.profile(),
        queryFn: async () => {
            const response = await userApi.getProfile();
            return response.data;
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
    });
};

export const useUpdateUserProfile = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (data: Partial<User>) => userApi.updateProfile(data),
        onSuccess: (response) => {
            // Update the profile cache
            queryClient.setQueryData(userQueryKeys.profile(), response.data);
            
            // Invalidate related queries
            queryClient.invalidateQueries({ queryKey: userQueryKeys.stats() });
            
            toast.success('Profile updated successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to update profile');
        },
    });
};

// Avatar Upload Hook
export const useUploadAvatar = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (file: File) => userApi.uploadAvatar(file),
        onSuccess: (response) => {
            // Update the profile cache with new avatar URL
            queryClient.setQueryData(userQueryKeys.profile(), (oldData: any) => {
                if (oldData) {
                    return {
                        ...oldData,
                        avatar: response.data?.avatarUrl,
                    };
                }
                return oldData;
            });
            
            toast.success('Avatar updated successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to upload avatar');
        },
    });
};

// User Stats Hook
export const useUserStats = () => {
    return useQuery({
        queryKey: userQueryKeys.stats(),
        queryFn: async () => {
            const response = await userApi.getUserStats();
            return response.data;
        },
        staleTime: 10 * 60 * 1000, // 10 minutes
        retry: 2,
    });
};

// Preferences Hooks
export const useUpdatePreferences = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (preferences: Partial<User['preferences']>) => 
            userApi.updatePreferences(preferences),
        onSuccess: (response) => {
            queryClient.setQueryData(userQueryKeys.profile(), response.data);
            toast.success('Preferences updated successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to update preferences');
        },
    });
};

// Location Update Hook
export const useUpdateLocation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (location: User['location']) => userApi.updateLocation(location),
        onSuccess: (response) => {
            queryClient.setQueryData(userQueryKeys.profile(), response.data);
            toast.success('Location updated successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to update location');
        },
    });
};

// Contact Info Update Hook
export const useUpdateContactInfo = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (contactInfo: { phone?: string; email?: string }) => 
            userApi.updateContactInfo(contactInfo),
        onSuccess: (response) => {
            queryClient.setQueryData(userQueryKeys.profile(), response.data);
            toast.success('Contact information updated successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to update contact information');
        },
    });
};

// Achievements Hook
export const useUserAchievements = () => {
    return useQuery({
        queryKey: userQueryKeys.achievements(),
        queryFn: async () => {
            const response = await userApi.getAchievements();
            return response.data;
        },
        staleTime: 15 * 60 * 1000, // 15 minutes
        retry: 2,
    });
};

// Notification Preferences Hook
export const useUpdateNotificationPreferences = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (notifications: {
            emailNotifications?: boolean;
            pushNotifications?: boolean;
            jobAlerts?: boolean;
            marketingEmails?: boolean;
        }) => userApi.updateNotificationPreferences(notifications),
        onSuccess: (response) => {
            queryClient.setQueryData(userQueryKeys.profile(), response.data);
            toast.success('Notification preferences updated successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to update notification preferences');
        },
    });
};

// Activity Log Hook
export const useUserActivity = (params?: {
    page?: number;
    limit?: number;
    type?: string;
}) => {
    return useQuery({
        queryKey: userQueryKeys.activity(params),
        queryFn: async () => {
            const response = await userApi.getActivityLog(params);
            return response.data;
        },
        staleTime: 2 * 60 * 1000, // 2 minutes
        retry: 2,
    });
};

// Theme Update Hook
export const useUpdateTheme = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (theme: 'light' | 'dark' | 'system') => userApi.updateTheme(theme),
        onSuccess: (response) => {
            queryClient.setQueryData(userQueryKeys.profile(), response.data);
            toast.success('Theme updated successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to update theme');
        },
    });
};

// Language Update Hook
export const useUpdateLanguage = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (language: string) => userApi.updateLanguage(language),
        onSuccess: (response) => {
            queryClient.setQueryData(userQueryKeys.profile(), response.data);
            toast.success('Language updated successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to update language');
        },
    });
};

// Timezone Update Hook
export const useUpdateTimezone = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (timezone: string) => userApi.updateTimezone(timezone),
        onSuccess: (response) => {
            queryClient.setQueryData(userQueryKeys.profile(), response.data);
            toast.success('Timezone updated successfully');
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to update timezone');
        },
    });
};

// Account Deactivation Hook
export const useDeactivateAccount = () => {
    return useMutation({
        mutationFn: () => userApi.deactivateAccount(),
        onSuccess: () => {
            toast.success('Account deactivated successfully');
            // Redirect to login or handle sign out
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to deactivate account');
        },
    });
};

// Data Export Hook
export const useExportUserData = () => {
    return useMutation({
        mutationFn: () => userApi.exportData(),
        onSuccess: (response) => {
            if (response.data?.downloadUrl) {
                window.open(response.data.downloadUrl, '_blank');
                toast.success('Data export initiated. Download will start shortly.');
            }
        },
        onError: (error: any) => {
            toast.error(error.message || 'Failed to export data');
        },
    });
};
