import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>, ArrowRight } from "lucide-react";
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion";
import VideoModal from "@/components/landing-page/VideoModal";
import SignupForm from "@/components/landing-page/SignupForm";
import TrustBadges from "@/components/landing-page/TrustBadges";
import FloatingActionButton from "@/components/landing-page/FloatingActionButton";
import LiveActivityIndicator from "@/components/landing-page/LiveActivityIndicator";
import ProgressIndicator from "@/components/landing-page/ProgressIndicator";
import MouseFollower from "@/components/landing-page/MouseFollower";
import { useToast } from "@/hooks/use-toast";
import AboutSection from "@/components/landing-page/AboutSection";
import SecurityBadges from "@/components/landing-page/SecurityBadges";
import BlogPreview from "@/components/landing-page/BlogPreview";
import NewsletterSignup from "@/components/landing-page/NewsletterSignup";
import IntegrationShowcase from "@/components/landing-page/IntegrationShowcase";
import HeroSection from "@/components/landing-page/HeroSection";
import FeaturesSection from "@/components/landing-page/FeaturesSection";
import TestimonialsSection from "@/components/landing-page/TestimonialsSection";
import PricingSection from "@/components/landing-page/PricingSection";
import KeyMetricsSection from "@/components/landing-page/KeyMetricsSection";
import InteractiveDemo from "@/components/landing-page/InteractiveDemo";
import { Upload, Zap, Target, TrendingUp } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import LiveChatWidget from "@/components/landing-page/LiveChatWidget";

const Index = () => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isScrolled, setIsScrolled] = useState(false);
    const [showVideoModal, setShowVideoModal] = useState(false);
    const [showSignupForm, setShowSignupForm] = useState(false);
    const { toast } = useToast();

    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 50);
        };

        // Smooth scrolling for anchor links
        const handleAnchorClick = (e: Event) => {
            const target = e.target as HTMLAnchorElement;
            if (target.hash) {
                e.preventDefault();
                const element = document.querySelector(target.hash);
                element?.scrollIntoView({ behavior: "smooth" });
            }
        };

        window.addEventListener("scroll", handleScroll);
        document.addEventListener("click", handleAnchorClick);

        return () => {
            window.removeEventListener("scroll", handleScroll);
            document.removeEventListener("click", handleAnchorClick);
        };
    }, []);

    const handleCTAClick = (action: string) => {
        if (action === "signup") {
            setShowSignupForm(true);
        } else if (action === "demo") {
            setShowVideoModal(true);
        } else {
            toast({
                title: "Feature Coming Soon!",
                description: `The ${action} feature will be available soon.`,
            });
        }
    };

    return (
        <div className="min-h-screen bg-white font-inter relative">
            {/* Modern Features */}
            <ProgressIndicator />
            <MouseFollower />
            <LiveChatWidget />
            <LiveActivityIndicator />

            {/* Navigation */}
            <nav
                className={`fixed top-0 w-full z-50 transition-all duration-300 ${
                    isScrolled
                        ? "bg-white/90 backdrop-blur-md shadow-sm"
                        : "bg-transparent"
                }`}
            >
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <h1 className="text-2xl font-jakarta font-bold text-slate-900">
                                Career
                                <span className="text-brand-blue-500">
                                    Algo
                                </span>
                            </h1>
                        </div>

                        <div className="hidden md:block">
                            <div className="ml-10 flex items-baseline space-x-8">
                                <a
                                    href="#features"
                                    className="text-gray-600 hover:text-brand-blue-500 px-3 py-2 text-sm font-medium transition-colors"
                                >
                                    Features
                                </a>
                                <a
                                    href="#how-it-works"
                                    className="text-gray-600 hover:text-brand-blue-500 px-3 py-2 text-sm font-medium transition-colors"
                                >
                                    How It Works
                                </a>
                                <a
                                    href="#pricing"
                                    className="text-gray-600 hover:text-brand-blue-500 px-3 py-2 text-sm font-medium transition-colors"
                                >
                                    Pricing
                                </a>
                                <a
                                    href="#about"
                                    className="text-gray-600 hover:text-brand-blue-500 px-3 py-2 text-sm font-medium transition-colors"
                                >
                                    About
                                </a>
                            </div>
                        </div>

                        <div className="hidden md:block">
                            <Button
                                onClick={() => handleCTAClick("signup")}
                                className="bg-brand-blue-500 hover:bg-brand-blue-600 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105"
                            >
                                Get Started Free
                            </Button>
                        </div>

                        <div className="md:hidden">
                            <button
                                onClick={() => setIsMenuOpen(!isMenuOpen)}
                                className="text-gray-600 hover:text-gray-900"
                            >
                                {isMenuOpen ? (
                                    <X size={24} />
                                ) : (
                                    <Menu size={24} />
                                )}
                            </button>
                        </div>
                    </div>
                </div>

                {/* Mobile menu */}
                {isMenuOpen && (
                    <div className="md:hidden bg-white border-t shadow-lg">
                        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                            <a
                                href="#features"
                                className="block px-3 py-2 text-gray-600 hover:text-brand-blue-500"
                            >
                                Features
                            </a>
                            <a
                                href="#how-it-works"
                                className="block px-3 py-2 text-gray-600 hover:text-brand-blue-500"
                            >
                                How It Works
                            </a>
                            <a
                                href="#pricing"
                                className="block px-3 py-2 text-gray-600 hover:text-brand-blue-500"
                            >
                                Pricing
                            </a>
                            <a
                                href="#about"
                                className="block px-3 py-2 text-gray-600 hover:text-brand-blue-500"
                            >
                                About
                            </a>
                            <Button
                                onClick={() => handleCTAClick("signup")}
                                className="w-full mt-4 bg-brand-blue-500 hover:bg-brand-blue-600 text-white"
                            >
                                Get Started Free
                            </Button>
                        </div>
                    </div>
                )}
            </nav>

            {/* Hero Section */}
            <HeroSection onCTAClick={handleCTAClick} />

            {/* Trust Badges */}
            <section className="py-8 bg-gray-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <TrustBadges />
                </div>
            </section>

            {/* Security Badges */}
            <SecurityBadges />

            {/* Social Proof Bar */}
            <section className="py-12 bg-gray-50 border-y">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <p className="text-center text-gray-600 mb-8 font-medium">
                        Trusted by professionals at leading companies
                    </p>
                    <div className="flex items-center justify-center gap-8 md:gap-12 flex-wrap opacity-60">
                        {[
                            "Google",
                            "Microsoft",
                            "Netflix",
                            "Apple",
                            "Amazon",
                            "Tesla",
                        ].map((company) => (
                            <div
                                key={company}
                                className="text-2xl font-bold text-gray-400 hover:text-gray-600 transition-colors cursor-pointer"
                            >
                                {company}
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Key Metrics Section */}
            <KeyMetricsSection />

            {/* Features Section */}
            <FeaturesSection onCTAClick={handleCTAClick} />

            {/* How It Works Section - Modernized and Interactive */}
            <section
                id="how-it-works"
                className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-gray-50 to-blue-50/30 relative overflow-hidden"
            >
                {/* Background decorations */}
                <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/5 to-purple-400/5 rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/5 to-pink-400/5 rounded-full blur-3xl"></div>

                <div className="max-w-7xl mx-auto relative">
                    <div className="text-center mb-16">
                        <Badge
                            variant="outline"
                            className="mb-4 text-brand-blue-500 border-brand-blue-200 bg-brand-blue-50/80 backdrop-blur-sm"
                        >
                            Simple Process, Powerful Results
                        </Badge>
                        <h2 className="text-4xl font-jakarta font-bold text-slate-900 mb-4">
                            Your Path to Career Success in{" "}
                            <span className="text-transparent bg-gradient-to-r from-brand-blue-500 to-purple-600 bg-clip-text">
                                4 Simple Steps
                            </span>
                        </h2>
                        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                            Experience the future of career advancement with our
                            AI-powered platform
                        </p>
                    </div>

                    <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
                        {/* Interactive Demo */}
                        <div className="order-2 lg:order-1">
                            <InteractiveDemo />
                        </div>

                        {/* Steps List */}
                        <div className="order-1 lg:order-2 space-y-8">
                            {[
                                {
                                    step: "1",
                                    title: "Upload Your Resume",
                                    description:
                                        "Simply drag and drop your current resume. Our AI instantly analyzes structure, content, and optimization opportunities.",
                                    icon: Upload,
                                    color: "from-blue-500 to-blue-600",
                                },
                                {
                                    step: "2",
                                    title: "AI Enhancement Magic",
                                    description:
                                        "Get personalized suggestions for keywords, formatting, and content that increase your ATS pass rate by 78%.",
                                    icon: Zap,
                                    color: "from-yellow-500 to-orange-500",
                                },
                                {
                                    step: "3",
                                    title: "Smart Job Matching",
                                    description:
                                        "Discover opportunities that perfectly match your enhanced profile with 95%+ compatibility scores.",
                                    icon: Target,
                                    color: "from-green-500 to-emerald-500",
                                },
                                {
                                    step: "4",
                                    title: "Land Your Dream Job",
                                    description:
                                        "Apply with confidence using AI-powered interview prep and personalized application strategies.",
                                    icon: TrendingUp,
                                    color: "from-purple-500 to-pink-500",
                                },
                            ].map((item, index) => {
                                const StepIcon = item.icon;
                                return (
                                    <div key={index} className="group relative">
                                        <div className="flex items-start gap-6">
                                            <div className="relative flex-shrink-0">
                                                <div
                                                    className={`w-16 h-16 bg-gradient-to-r ${item.color} rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110`}
                                                >
                                                    {item.step}
                                                </div>
                                                <div
                                                    className={`absolute inset-0 bg-gradient-to-r ${item.color} rounded-2xl blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-300`}
                                                ></div>

                                                {index < 3 && (
                                                    <div className="absolute top-16 left-1/2 transform -translate-x-1/2 w-0.5 h-12 bg-gradient-to-b from-gray-200 to-transparent"></div>
                                                )}
                                            </div>

                                            <div className="flex-1 pt-2">
                                                <div className="flex items-center gap-3 mb-3">
                                                    <h3 className="text-xl font-jakarta font-semibold text-slate-900 group-hover:text-brand-blue-600 transition-colors">
                                                        {item.title}
                                                    </h3>
                                                    <div
                                                        className={`p-2 rounded-lg bg-gradient-to-r ${item.color} opacity-20 group-hover:opacity-30 transition-opacity`}
                                                    >
                                                        <StepIcon className="w-5 h-5 text-gray-700" />
                                                    </div>
                                                </div>
                                                <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors">
                                                    {item.description}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>

                    <div className="text-center">
                        <Button
                            size="lg"
                            onClick={() => handleCTAClick("signup")}
                            className="bg-gradient-to-r from-brand-blue-500 to-purple-600 hover:from-brand-blue-600 hover:to-purple-700 text-white px-8 py-4 text-lg font-medium rounded-lg transition-all duration-200 hover:scale-105 shadow-xl"
                        >
                            Start Your Career Transformation
                        </Button>
                        <p className="text-sm text-gray-500 mt-3">
                            Join 50,000+ professionals who've accelerated their
                            careers
                        </p>
                    </div>
                </div>
            </section>

            {/* About Section */}
            <AboutSection />

            {/* Integration Showcase */}
            <IntegrationShowcase />

            {/* Testimonials Section */}
            <TestimonialsSection />

            {/* Blog Preview */}
            <BlogPreview />

            {/* Newsletter Signup */}
            <NewsletterSignup />

            {/* Pricing Section */}
            <PricingSection />

            {/* FAQ Section */}
            <section className="py-20 bg-gray-50 px-4 sm:px-6 lg:px-8">
                <div className="max-w-4xl mx-auto">
                    <div className="text-center mb-16">
                        <h2 className="text-4xl font-jakarta font-bold text-slate-900 mb-4">
                            Frequently Asked Questions
                        </h2>
                    </div>

                    <Accordion type="single" collapsible className="space-y-4">
                        {[
                            {
                                question:
                                    "How does the AI resume enhancement work?",
                                answer: "Our AI analyzes your resume against industry standards and job requirements, providing specific suggestions for improvements in content, formatting, and keyword optimization to increase your chances of passing ATS systems.",
                            },
                            {
                                question: "Is my data secure and private?",
                                answer: "Yes, we use enterprise-grade encryption and follow strict data privacy protocols. Your personal information is never shared with third parties without your explicit consent, and you maintain full control over your data.",
                            },
                            {
                                question: "Can I cancel anytime?",
                                answer: "Absolutely! You can cancel your subscription at any time with no questions asked. Your account will remain active until the end of your current billing period.",
                            },
                            {
                                question: "Do you offer refunds?",
                                answer: "We offer a 30-day money-back guarantee. If you're not satisfied with our service within the first 30 days, we'll provide a full refund.",
                            },
                            {
                                question: "How accurate is the job matching?",
                                answer: "Our AI achieves 95%+ compatibility rates by analyzing multiple factors including skills, experience, location preferences, salary expectations, and career goals to match you with relevant opportunities.",
                            },
                            {
                                question: "What file formats do you support?",
                                answer: "We support all major resume formats including PDF, DOC, DOCX, and can also import directly from LinkedIn profiles for seamless integration.",
                            },
                        ].map((faq, index) => (
                            <AccordionItem
                                key={index}
                                value={`item-${index}`}
                                className="bg-white rounded-lg px-6"
                            >
                                <AccordionTrigger className="text-left font-semibold text-slate-900 hover:text-brand-blue-500">
                                    {faq.question}
                                </AccordionTrigger>
                                <AccordionContent className="text-gray-600 leading-relaxed">
                                    {faq.answer}
                                </AccordionContent>
                            </AccordionItem>
                        ))}
                    </Accordion>
                </div>
            </section>

            {/* Final CTA Section */}
            <section className="py-20 bg-gradient-to-r from-brand-blue-500 to-brand-blue-600 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
                <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>
                <div className="max-w-4xl mx-auto text-center relative">
                    <h2 className="text-4xl font-jakarta font-bold text-white mb-4">
                        Ready to Transform Your Career?
                    </h2>
                    <p className="text-xl text-blue-100 mb-8">
                        Join 50,000+ professionals who've accelerated their
                        careers with AI
                    </p>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                        <Button
                            size="lg"
                            onClick={() => handleCTAClick("signup")}
                            className="bg-white text-brand-blue-500 hover:bg-gray-100 px-8 py-4 text-lg font-medium rounded-lg transition-all duration-200 hover:scale-105 shadow-lg"
                        >
                            Start Free Trial
                        </Button>
                        <Button
                            size="lg"
                            variant="outline"
                            onClick={() => handleCTAClick("demo")}
                            className="border-white text-white hover:bg-white hover:text-brand-blue-500 px-8 py-4 text-lg font-medium rounded-lg transition-all duration-200"
                        >
                            Schedule Demo
                        </Button>
                    </div>

                    <p className="text-blue-100 text-sm">
                        No credit card required • Cancel anytime • 30-day
                        guarantee
                    </p>
                </div>
            </section>

            {/* Footer */}
            <footer className="bg-slate-900 text-white py-16 px-4 sm:px-6 lg:px-8">
                <div className="max-w-7xl mx-auto">
                    <div className="grid md:grid-cols-4 gap-8 mb-8">
                        <div>
                            <h3 className="text-xl font-jakarta font-bold mb-4">
                                Career
                                <span className="text-brand-blue-400">
                                    Algo
                                </span>
                            </h3>
                            <p className="text-gray-400 mb-4">
                                Craft your career with AI-powered tools and land
                                your dream job faster.
                            </p>
                            <div className="flex gap-4">
                                {["Twitter", "LinkedIn", "Facebook"].map(
                                    (social) => (
                                        <a
                                            key={social}
                                            href="#"
                                            className="text-gray-400 hover:text-white transition-colors"
                                        >
                                            {social}
                                        </a>
                                    )
                                )}
                            </div>
                        </div>

                        <div>
                            <h4 className="font-semibold mb-4">Company</h4>
                            <ul className="space-y-2 text-gray-400">
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                    >
                                        About
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                    >
                                        Careers
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                    >
                                        Press
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                    >
                                        Contact
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h4 className="font-semibold mb-4">Product</h4>
                            <ul className="space-y-2 text-gray-400">
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                    >
                                        Features
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                    >
                                        Pricing
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                    >
                                        API
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                    >
                                        Integrations
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <div>
                            <h4 className="font-semibold mb-4">Resources</h4>
                            <ul className="space-y-2 text-gray-400">
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                    >
                                        Blog
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                    >
                                        Help Center
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                    >
                                        Community
                                    </a>
                                </li>
                                <li>
                                    <a
                                        href="#"
                                        className="hover:text-white transition-colors"
                                    >
                                        Tutorials
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
                        <p className="text-gray-400 text-sm">
                            © 2024 CareerAlgo. All rights reserved.
                        </p>
                        <div className="flex gap-6 mt-4 md:mt-0">
                            <a
                                href="#"
                                className="text-gray-400 hover:text-white text-sm transition-colors"
                            >
                                Privacy Policy
                            </a>
                            <a
                                href="#"
                                className="text-gray-400 hover:text-white text-sm transition-colors"
                            >
                                Terms of Service
                            </a>
                            <a
                                href="#"
                                className="text-gray-400 hover:text-white text-sm transition-colors"
                            >
                                Cookie Policy
                            </a>
                        </div>
                    </div>
                </div>
            </footer>

            {/* Modals */}
            <VideoModal
                isOpen={showVideoModal}
                onClose={() => setShowVideoModal(false)}
            />
            <SignupForm
                isOpen={showSignupForm}
                onClose={() => setShowSignupForm(false)}
            />
        </div>
    );
};

export default Index;
