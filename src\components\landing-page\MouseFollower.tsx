
import React, { useState, useEffect } from 'react';

const MouseFollower = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
      
      // Check if hovering over interactive elements
      const target = e.target as Element;
      const isInteractive = target.closest('button, a, input, [role="button"]');
      setIsHovering(!!isInteractive);
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <div
      className={`fixed pointer-events-none z-50 transition-all duration-200 ${
        isHovering ? 'scale-150 bg-brand-blue-500/20' : 'scale-100 bg-brand-blue-500/10'
      }`}
      style={{
        left: mousePosition.x - 10,
        top: mousePosition.y - 10,
        width: '20px',
        height: '20px',
        borderRadius: '50%',
        transform: `translate(-50%, -50%) scale(${isHovering ? 1.5 : 1})`,
      }}
    />
  );
};

export default MouseFollower;
