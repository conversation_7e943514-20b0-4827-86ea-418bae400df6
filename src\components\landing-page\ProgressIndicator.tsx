
import React, { useState, useEffect } from 'react';
import { Progress } from "@/components/ui/progress";

const ProgressIndicator = () => {
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (scrollTop / docHeight) * 100;
      setScrollProgress(progress);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      <Progress 
        value={scrollProgress} 
        className="h-1 rounded-none bg-transparent border-none"
      />
    </div>
  );
};

export default ProgressIndicator;
