import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import { SignedIn, SignedOut } from "@clerk/clerk-react";
import LoginPage from "./pages/auth/LoginPage";
import SignupPage from "./pages/auth/SignupPage";
import ForgotPasswordPage from "./pages/auth/ForgotPasswordPage";
import OnboardingFlow from "./pages/onboarding/OnboardingFlow";
import DashboardLayout from "./components/dashboard/DashboardLayout";
import DashboardHome from "./pages/dashboard/DashboardHome";
import ResumeHub from "./pages/dashboard/ResumeHub";
import ResumeEditor from "./pages/dashboard/ResumeEditor";
import ResumeBuilder from "./pages/dashboard/ResumeBuilder";
import JobSearch from "./pages/dashboard/JobSearch";
import AIAssistant from "./pages/dashboard/AIAssistant";
import Analytics from "./pages/dashboard/Analytics";
import Profile from "./pages/dashboard/Profile";
import Settings from "./pages/dashboard/Settings";
import { AuthProvider } from "./contexts/auth/AuthContext";
import { AuthenticatedRoute } from "./components/auth/ProtectedRoute";

const queryClient = new QueryClient();

const AppRoutes = () => {
    return (
        <Routes>
            {/* Public Auth Routes */}
            <Route path="/" element={<Index />} />
            <Route
                path="/login"
                element={
                    <SignedOut>
                        <LoginPage />
                    </SignedOut>
                }
            />
            <Route
                path="/signup"
                element={
                    <SignedOut>
                        <SignupPage />
                    </SignedOut>
                }
            />
            <Route
                path="/forgot-password"
                element={
                    <SignedOut>
                        <ForgotPasswordPage />
                    </SignedOut>
                }
            />

            {/* Onboarding Route */}
            <Route
                path="/onboarding"
                element={
                    <SignedIn>
                        <OnboardingFlow />
                    </SignedIn>
                }
            />

            {/* Protected Dashboard Routes */}
            <Route
                path="/dashboard"
                element={
                    <AuthenticatedRoute>
                        <DashboardLayout />
                    </AuthenticatedRoute>
                }
            >
                <Route index element={<DashboardHome />} />
                <Route path="resume" element={<ResumeHub />} />
                <Route path="resume/editor/:id" element={<ResumeEditor />} />
                <Route path="resume/builder" element={<ResumeBuilder />} />
                <Route path="resume/builder/:id" element={<ResumeBuilder />} />
                <Route path="jobs" element={<JobSearch />} />
                <Route path="ai-assistant" element={<AIAssistant />} />
                <Route path="analytics" element={<Analytics />} />
                <Route path="profile" element={<Profile />} />
                <Route path="settings" element={<Settings />} />
            </Route>

            {/* Redirects */}
            <Route
                path="/"
                element={
                    <SignedIn>
                        <Navigate to="/dashboard" replace />
                    </SignedIn>
                }
            />
            <Route
                path="/"
                element={
                    <SignedOut>
                        <Navigate to="/login" replace />
                    </SignedOut>
                }
            />
            <Route path="*" element={<NotFound />} />
        </Routes>
    );
};

const App = () => (
    <QueryClientProvider client={queryClient}>
        <AuthProvider>
            <TooltipProvider>
                <Toaster />
                <Sonner />
                <BrowserRouter>
                    <AppRoutes />
                </BrowserRouter>
            </TooltipProvider>
        </AuthProvider>
    </QueryClientProvider>
);

export default App;
