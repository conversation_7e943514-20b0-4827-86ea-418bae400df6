@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Plus+Jakarta+Sans:wght@400;500;600;700;800&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 222.2 84% 4.9%;

        --card: 0 0% 100%;
        --card-foreground: 222.2 84% 4.9%;

        --popover: 0 0% 100%;
        --popover-foreground: 222.2 84% 4.9%;

        --primary: 221.2 83.2% 53.3%;
        --primary-foreground: 210 40% 98%;

        --secondary: 210 40% 96.1%;
        --secondary-foreground: 222.2 47.4% 11.2%;

        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;

        --accent: 210 40% 96.1%;
        --accent-foreground: 222.2 47.4% 11.2%;

        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;

        --border: 214.3 31.8% 91.4%;
        --input: 214.3 31.8% 91.4%;
        --ring: 221.2 83.2% 53.3%;

        --radius: 0.75rem;

        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;

        --sidebar-background: 217.2 32.6% 17.5%;
        --sidebar-foreground: 210 40% 98%;
        --sidebar-primary: 221.2 83.2% 53.3%;
        --sidebar-primary-foreground: 210 40% 98%;
        --sidebar-accent: 216 27.9% 22.9%;
        --sidebar-accent-foreground: 210 40% 98%;
        --sidebar-border: 216 27.9% 22.9%;
        --sidebar-ring: 221.2 83.2% 53.3%;
    }

    .dark {
        --background: 222.2 84% 4.9%;
        --foreground: 210 40% 98%;

        --card: 222.2 84% 4.9%;
        --card-foreground: 210 40% 98%;

        --popover: 222.2 84% 4.9%;
        --popover-foreground: 210 40% 98%;

        --primary: 210 40% 98%;
        --primary-foreground: 222.2 47.4% 11.2%;

        --secondary: 217.2 32.6% 17.5%;
        --secondary-foreground: 210 40% 98%;

        --muted: 217.2 32.6% 17.5%;
        --muted-foreground: 215 20.2% 65.1%;

        --accent: 217.2 32.6% 17.5%;
        --accent-foreground: 210 40% 98%;

        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 40% 98%;

        --border: 217.2 32.6% 17.5%;
        --input: 217.2 32.6% 17.5%;
        --ring: 212.7 26.8% 83.9%;

        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;

        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
    }
}

@layer utilities {
    .font-inter {
        font-family: "Inter", sans-serif;
    }

    .font-jakarta {
        font-family: "Plus Jakarta Sans", sans-serif;
    }

    .animate-float {
        animation: float 6s ease-in-out infinite;
    }

    .animate-spin-slow {
        animation: spin 8s linear infinite;
    }

    .animate-scale-in {
        animation: scaleIn 0.2s ease-out;
    }

    @keyframes float {
        0%,
        100% {
            transform: translateY(0px) rotate(3deg);
        }
        50% {
            transform: translateY(-10px) rotate(6deg);
        }
    }

    @keyframes scaleIn {
        0% {
            transform: scale(0.95);
            opacity: 0;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }
}

@layer components {
    .hover-scale {
        @apply transition-transform duration-200 hover:scale-105;
    }

    .gradient-border {
        @apply relative bg-gradient-to-r from-primary to-primary-hover p-[1px] rounded-lg;
    }

    .gradient-border::before {
        content: "";
        @apply absolute inset-0 bg-white rounded-lg;
        z-index: -1;
    }
}
