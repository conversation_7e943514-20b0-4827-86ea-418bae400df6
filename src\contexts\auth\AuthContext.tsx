import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth as useClerkAuth, useUser } from '@clerk/clerk-react';
import { setTokenFunction } from '@/lib/api/client';
import { User } from '@/lib/types/api';

// Auth Context Types
interface AuthContextType {
    // Clerk auth state
    isLoaded: boolean;
    isSignedIn: boolean;
    user: any; // Clerk user object
    
    // Our app user state
    appUser: User | null;
    isLoadingUser: boolean;
    
    // Auth methods
    getToken: () => Promise<string | null>;
    signOut: () => Promise<void>;
    refreshUser: () => Promise<void>;
    
    // Subscription helpers
    isPremium: boolean;
    isAdmin: boolean;
    canAccess: (feature: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth Provider Component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { isLoaded, isSignedIn, getToken, signOut } = useClerkAuth();
    const { user } = useUser();
    
    const [appUser, setAppUser] = useState<User | null>(null);
    const [isLoadingUser, setIsLoadingUser] = useState(false);

    // Set up token function for API client
    useEffect(() => {
        setTokenFunction(getToken);
    }, [getToken]);

    // Fetch app user data when Clerk user is available
    useEffect(() => {
        if (isSignedIn && user && !appUser && !isLoadingUser) {
            fetchAppUser();
        }
    }, [isSignedIn, user, appUser, isLoadingUser]);

    const fetchAppUser = async () => {
        if (!isSignedIn || !user) return;
        
        setIsLoadingUser(true);
        try {
            // TODO: Implement API call to fetch user profile
            // For now, create a mock user from Clerk data
            const mockUser: User = {
                id: user.id,
                clerkUserId: user.id,
                email: user.primaryEmailAddress?.emailAddress || '',
                firstName: user.firstName || '',
                lastName: user.lastName || '',
                avatar: user.imageUrl,
                preferences: {
                    theme: 'system',
                    language: 'en',
                    timezone: 'UTC',
                    emailNotifications: true,
                    pushNotifications: true,
                    jobAlerts: true,
                    marketingEmails: false,
                },
                subscription: {
                    plan: 'FREE',
                    status: 'ACTIVE',
                    startDate: new Date().toISOString(),
                    features: ['basic_resume', 'basic_job_search'],
                    usage: {
                        resumeAnalyses: 0,
                        jobApplications: 0,
                        interviewSessions: 0,
                        maxResumeAnalyses: 3,
                        maxJobApplications: 10,
                        maxInterviewSessions: 5,
                    },
                },
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            };
            
            setAppUser(mockUser);
        } catch (error) {
            console.error('Failed to fetch user profile:', error);
        } finally {
            setIsLoadingUser(false);
        }
    };

    const refreshUser = async () => {
        if (isSignedIn && user) {
            await fetchAppUser();
        }
    };

    const handleSignOut = async () => {
        setAppUser(null);
        await signOut();
    };

    // Subscription helpers
    const isPremium = appUser?.subscription.plan === 'PREMIUM' || appUser?.subscription.plan === 'ENTERPRISE';
    const isAdmin = user?.publicMetadata?.role === 'admin';

    const canAccess = (feature: string): boolean => {
        if (!appUser) return false;
        
        const { features } = appUser.subscription;
        return features.includes(feature);
    };

    const value: AuthContextType = {
        // Clerk auth state
        isLoaded,
        isSignedIn: isSignedIn || false,
        user,
        
        // App user state
        appUser,
        isLoadingUser,
        
        // Auth methods
        getToken,
        signOut: handleSignOut,
        refreshUser,
        
        // Subscription helpers
        isPremium,
        isAdmin,
        canAccess,
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

// Hook for checking authentication status
export const useAuthStatus = () => {
    const { isLoaded, isSignedIn, isLoadingUser } = useAuth();
    
    return {
        isLoading: !isLoaded || isLoadingUser,
        isAuthenticated: isSignedIn,
        isReady: isLoaded && !isLoadingUser,
    };
};

// Hook for subscription features
export const useSubscription = () => {
    const { appUser, isPremium, isAdmin, canAccess } = useAuth();
    
    return {
        subscription: appUser?.subscription,
        isPremium,
        isAdmin,
        canAccess,
        usage: appUser?.subscription.usage,
        features: appUser?.subscription.features || [],
    };
};
