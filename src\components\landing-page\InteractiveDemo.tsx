
import React, { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, Upload, Zap, Target, TrendingUp, FileText } from "lucide-react";

const InteractiveDemo = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const steps = [
    {
      id: 1,
      title: "Upload Resume",
      description: "AI analyzes your current resume",
      icon: Upload,
      content: "Analyzing resume structure, keywords, and formatting...",
      color: "from-blue-500 to-blue-600"
    },
    {
      id: 2,
      title: "AI Enhancement",
      description: "Smart optimization suggestions",
      icon: Zap,
      content: "Found 12 improvement opportunities. ATS score: 85% → 96%",
      color: "from-yellow-500 to-orange-500"
    },
    {
      id: 3,
      title: "Job Matching",
      description: "Finding perfect opportunities",
      icon: Target,
      content: "3 high-compatibility matches found. Average salary: $95k",
      color: "from-green-500 to-emerald-500"
    },
    {
      id: 4,
      title: "Success!",
      description: "Ready to apply with confidence",
      icon: TrendingUp,
      content: "78% higher chance of getting interviews. Let's go!",
      color: "from-purple-500 to-pink-500"
    }
  ];

  useEffect(() => {
    if (isAnimating) {
      const timer = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(timer);
            if (currentStep < steps.length - 1) {
              setTimeout(() => {
                setCurrentStep(prev => prev + 1);
                setProgress(0);
              }, 500);
            } else {
              setIsAnimating(false);
            }
            return 100;
          }
          return prev + 2;
        });
      }, 50);
      
      return () => clearInterval(timer);
    }
  }, [currentStep, isAnimating, steps.length]);

  const startDemo = () => {
    setCurrentStep(0);
    setProgress(0);
    setIsAnimating(true);
  };

  const resetDemo = () => {
    setCurrentStep(0);
    setProgress(0);
    setIsAnimating(false);
  };

  const step = steps[currentStep];
  const StepIcon = step.icon;

  return (
    <Card className="w-full max-w-md mx-auto bg-white shadow-xl border-0">
      <CardContent className="p-6">
        <div className="text-center mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            See CareerAlgo in Action
          </h3>
          <p className="text-sm text-gray-600">
            Watch how AI transforms your career in real-time
          </p>
        </div>

        {/* Demo Screen */}
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 mb-6 min-h-[200px] flex flex-col items-center justify-center relative overflow-hidden">
          {/* Background Animation */}
          <div className={`absolute inset-0 bg-gradient-to-r ${step.color} opacity-10 transition-all duration-500`}></div>
          
          {/* Step Icon */}
          <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${step.color} flex items-center justify-center mb-4 transform transition-all duration-500 ${isAnimating ? 'scale-110 animate-pulse' : 'scale-100'}`}>
            <StepIcon className="w-8 h-8 text-white" />
          </div>

          {/* Step Content */}
          <div className="text-center space-y-2">
            <h4 className="text-lg font-semibold text-gray-900">{step.title}</h4>
            <p className="text-sm text-gray-600">{step.description}</p>
            
            {isAnimating && (
              <div className="mt-4 w-full">
                <Progress value={progress} className="h-2" />
                <p className="text-xs text-gray-500 mt-2">{step.content}</p>
              </div>
            )}
          </div>

          {/* Success Animation */}
          {currentStep === steps.length - 1 && progress === 100 && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-green-500 rounded-full p-4 animate-bounce">
                <CheckCircle className="w-8 h-8 text-white" />
              </div>
            </div>
          )}
        </div>

        {/* Step Indicators */}
        <div className="flex justify-center gap-2 mb-6">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index <= currentStep ? 'bg-brand-blue-500' : 'bg-gray-200'
              }`}
            />
          ))}
        </div>

        {/* Demo Controls */}
        <div className="space-y-3">
          {!isAnimating ? (
            <Button
              onClick={startDemo}
              className="w-full bg-gradient-to-r from-brand-blue-500 to-purple-600 hover:from-brand-blue-600 hover:to-purple-700 text-white"
            >
              <FileText className="w-4 h-4 mr-2" />
              Start Interactive Demo
            </Button>
          ) : (
            <Button
              onClick={resetDemo}
              variant="outline"
              className="w-full"
            >
              Reset Demo
            </Button>
          )}
          
          <div className="flex justify-center gap-4 text-xs text-gray-500">
            <Badge variant="outline" className="text-xs">
              No signup required
            </Badge>
            <Badge variant="outline" className="text-xs">
              30 seconds
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default InteractiveDemo;
