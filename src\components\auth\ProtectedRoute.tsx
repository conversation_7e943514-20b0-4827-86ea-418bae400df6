import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth, useAuthStatus, useSubscription } from '@/contexts/auth/AuthContext';
import { Loader2 } from 'lucide-react';

// Protection levels
export enum ProtectionLevel {
    PUBLIC = 'public',
    AUTHENTICATED = 'authenticated',
    PREMIUM = 'premium',
    ADMIN = 'admin',
}

// Protected Route Props
interface ProtectedRouteProps {
    children: React.ReactNode;
    level: ProtectionLevel;
    fallback?: React.ReactNode;
    redirectTo?: string;
    requiredFeature?: string;
}

// Loading component
const LoadingScreen: React.FC = () => (
    <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-muted-foreground">Loading...</p>
        </div>
    </div>
);

// Unauthorized component
const UnauthorizedScreen: React.FC<{ message?: string }> = ({ 
    message = "You don't have permission to access this page." 
}) => (
    <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center max-w-md mx-auto p-6">
            <div className="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🔒</span>
            </div>
            <h1 className="text-2xl font-bold text-foreground mb-2">Access Denied</h1>
            <p className="text-muted-foreground mb-6">{message}</p>
            <button
                onClick={() => window.history.back()}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
                Go Back
            </button>
        </div>
    </div>
);

// Main Protected Route Component
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
    children,
    level,
    fallback,
    redirectTo,
    requiredFeature,
}) => {
    const location = useLocation();
    const { isLoading, isAuthenticated } = useAuthStatus();
    const { isPremium, isAdmin, canAccess } = useSubscription();

    // Show loading screen while checking auth
    if (isLoading) {
        return fallback || <LoadingScreen />;
    }

    // Handle different protection levels
    switch (level) {
        case ProtectionLevel.PUBLIC:
            // Public routes - no protection needed
            return <>{children}</>;

        case ProtectionLevel.AUTHENTICATED:
            if (!isAuthenticated) {
                return <Navigate 
                    to={redirectTo || '/login'} 
                    state={{ from: location }} 
                    replace 
                />;
            }
            
            // Check for required feature if specified
            if (requiredFeature && !canAccess(requiredFeature)) {
                return <UnauthorizedScreen 
                    message={`This feature requires a premium subscription.`} 
                />;
            }
            
            return <>{children}</>;

        case ProtectionLevel.PREMIUM:
            if (!isAuthenticated) {
                return <Navigate 
                    to={redirectTo || '/login'} 
                    state={{ from: location }} 
                    replace 
                />;
            }
            
            if (!isPremium) {
                return <UnauthorizedScreen 
                    message="This feature is only available for premium subscribers." 
                />;
            }
            
            return <>{children}</>;

        case ProtectionLevel.ADMIN:
            if (!isAuthenticated) {
                return <Navigate 
                    to={redirectTo || '/login'} 
                    state={{ from: location }} 
                    replace 
                />;
            }
            
            if (!isAdmin) {
                return <UnauthorizedScreen 
                    message="This area is restricted to administrators only." 
                />;
            }
            
            return <>{children}</>;

        default:
            return <UnauthorizedScreen message="Invalid protection level." />;
    }
};

// Convenience components for common protection levels
export const AuthenticatedRoute: React.FC<Omit<ProtectedRouteProps, 'level'>> = (props) => (
    <ProtectedRoute {...props} level={ProtectionLevel.AUTHENTICATED} />
);

export const PremiumRoute: React.FC<Omit<ProtectedRouteProps, 'level'>> = (props) => (
    <ProtectedRoute {...props} level={ProtectionLevel.PREMIUM} />
);

export const AdminRoute: React.FC<Omit<ProtectedRouteProps, 'level'>> = (props) => (
    <ProtectedRoute {...props} level={ProtectionLevel.ADMIN} />
);

// Permission wrapper component for conditional rendering
interface PermissionWrapperProps {
    permission: string;
    fallback?: React.ReactNode;
    children: React.ReactNode;
    requirePremium?: boolean;
    requireAdmin?: boolean;
}

export const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
    permission,
    fallback = null,
    children,
    requirePremium = false,
    requireAdmin = false,
}) => {
    const { isAuthenticated } = useAuthStatus();
    const { isPremium, isAdmin, canAccess } = useSubscription();

    // Check authentication
    if (!isAuthenticated) {
        return <>{fallback}</>;
    }

    // Check admin requirement
    if (requireAdmin && !isAdmin) {
        return <>{fallback}</>;
    }

    // Check premium requirement
    if (requirePremium && !isPremium) {
        return <>{fallback}</>;
    }

    // Check specific permission
    if (!canAccess(permission)) {
        return <>{fallback}</>;
    }

    return <>{children}</>;
};

// Hook for checking permissions
export const usePermissions = () => {
    const { isAuthenticated } = useAuthStatus();
    const { isPremium, isAdmin, canAccess } = useSubscription();

    const hasPermission = (permission: string, options?: {
        requirePremium?: boolean;
        requireAdmin?: boolean;
    }) => {
        if (!isAuthenticated) return false;
        if (options?.requireAdmin && !isAdmin) return false;
        if (options?.requirePremium && !isPremium) return false;
        return canAccess(permission);
    };

    return {
        isAuthenticated,
        isPremium,
        isAdmin,
        canAccess,
        hasPermission,
    };
};
