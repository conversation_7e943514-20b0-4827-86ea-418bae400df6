
import React, { useState, useEffect } from 'react';
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Users, Eye, Download, Star } from "lucide-react";

const LiveActivityIndicator = () => {
  const [activities, setActivities] = useState([
    { icon: Users, text: "23 people viewing this page", color: "bg-brand-green-500" },
    { icon: Download, text: "142 resumes enhanced today", color: "bg-brand-blue-500" },
    { icon: Star, text: "<PERSON> just got hired at Google!", color: "bg-yellow-500" },
    { icon: Eye, text: "89 interviews scheduled this week", color: "bg-purple-500" }
  ]);
  
  const [currentActivity, setCurrentActivity] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentActivity((prev) => (prev + 1) % activities.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [activities.length]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
    }, 15000);

    return () => clearTimeout(timer);
  }, []);

  if (!isVisible) return null;

  const activity = activities[currentActivity];
  const Icon = activity.icon;

  return (
    <div className="fixed bottom-6 left-6 z-40 animate-fade-in">
      <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
        <CardContent className="p-3">
          <div className="flex items-center gap-3">
            <div className={`w-8 h-8 ${activity.color} rounded-full flex items-center justify-center animate-pulse`}>
              <Icon size={16} className="text-white" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">{activity.text}</p>
            </div>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-gray-600 text-xs"
            >
              ✕
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LiveActivityIndicator;
