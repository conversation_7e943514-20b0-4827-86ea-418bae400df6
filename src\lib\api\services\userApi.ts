import { api } from '../client';
import { API_ENDPOINTS } from '../endpoints';
import { ApiResponse, User } from '@/lib/types/api';

// User Stats Interface
export interface UserStats {
    totalResumes: number;
    totalApplications: number;
    totalInterviews: number;
    responseRate: number;
    profileCompleteness: number;
    lastActivity: string;
    joinDate: string;
    achievements: Achievement[];
}

export interface Achievement {
    id: string;
    title: string;
    description: string;
    icon: string;
    unlockedAt: string;
    category: 'RESUME' | 'APPLICATION' | 'INTERVIEW' | 'SKILL' | 'MILESTONE';
}

// User API Service Class
export class UserAPI {
    /**
     * Get current user profile
     */
    async getProfile(): Promise<ApiResponse<User>> {
        const response = await api.get<ApiResponse<User>>(API_ENDPOINTS.users.profile);
        return response.data;
    }

    /**
     * Update user profile
     */
    async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
        const response = await api.put<ApiResponse<User>>(
            API_ENDPOINTS.users.profile, 
            data
        );
        return response.data;
    }

    /**
     * Upload user avatar
     */
    async uploadAvatar(file: File): Promise<ApiResponse<{ avatarUrl: string }>> {
        const formData = new FormData();
        formData.append('avatar', file);
        
        const response = await api.post<ApiResponse<{ avatarUrl: string }>>(
            API_ENDPOINTS.users.avatar,
            formData,
            {
                headers: { 'Content-Type': 'multipart/form-data' },
            }
        );
        return response.data;
    }

    /**
     * Get user statistics
     */
    async getUserStats(): Promise<ApiResponse<UserStats>> {
        const response = await api.get<ApiResponse<UserStats>>(API_ENDPOINTS.users.stats);
        return response.data;
    }

    /**
     * Update user preferences
     */
    async updatePreferences(preferences: Partial<User['preferences']>): Promise<ApiResponse<User>> {
        const response = await api.patch<ApiResponse<User>>(
            API_ENDPOINTS.users.profile,
            { preferences }
        );
        return response.data;
    }

    /**
     * Deactivate user account
     */
    async deactivateAccount(): Promise<ApiResponse<void>> {
        const response = await api.post<ApiResponse<void>>(API_ENDPOINTS.users.deactivate);
        return response.data;
    }

    /**
     * Export user data
     */
    async exportData(): Promise<ApiResponse<{ downloadUrl: string }>> {
        const response = await api.post<ApiResponse<{ downloadUrl: string }>>(
            API_ENDPOINTS.users.exportData
        );
        return response.data;
    }

    /**
     * Update user location
     */
    async updateLocation(location: User['location']): Promise<ApiResponse<User>> {
        const response = await api.patch<ApiResponse<User>>(
            API_ENDPOINTS.users.profile,
            { location }
        );
        return response.data;
    }

    /**
     * Update user contact information
     */
    async updateContactInfo(contactInfo: {
        phone?: string;
        email?: string;
    }): Promise<ApiResponse<User>> {
        const response = await api.patch<ApiResponse<User>>(
            API_ENDPOINTS.users.profile,
            contactInfo
        );
        return response.data;
    }

    /**
     * Get user achievements
     */
    async getAchievements(): Promise<ApiResponse<Achievement[]>> {
        const response = await api.get<ApiResponse<Achievement[]>>(
            `${API_ENDPOINTS.users.profile}/achievements`
        );
        return response.data;
    }

    /**
     * Update notification preferences
     */
    async updateNotificationPreferences(notifications: {
        emailNotifications?: boolean;
        pushNotifications?: boolean;
        jobAlerts?: boolean;
        marketingEmails?: boolean;
    }): Promise<ApiResponse<User>> {
        const response = await api.patch<ApiResponse<User>>(
            API_ENDPOINTS.users.profile,
            { 
                preferences: {
                    ...notifications
                }
            }
        );
        return response.data;
    }

    /**
     * Change user password (if using custom auth)
     */
    async changePassword(data: {
        currentPassword: string;
        newPassword: string;
    }): Promise<ApiResponse<void>> {
        const response = await api.post<ApiResponse<void>>(
            `${API_ENDPOINTS.users.profile}/change-password`,
            data
        );
        return response.data;
    }

    /**
     * Verify email address
     */
    async verifyEmail(token: string): Promise<ApiResponse<void>> {
        const response = await api.post<ApiResponse<void>>(
            `${API_ENDPOINTS.users.profile}/verify-email`,
            { token }
        );
        return response.data;
    }

    /**
     * Request email verification
     */
    async requestEmailVerification(): Promise<ApiResponse<void>> {
        const response = await api.post<ApiResponse<void>>(
            `${API_ENDPOINTS.users.profile}/request-email-verification`
        );
        return response.data;
    }

    /**
     * Get user activity log
     */
    async getActivityLog(params?: {
        page?: number;
        limit?: number;
        type?: string;
    }): Promise<ApiResponse<any[]>> {
        const response = await api.get<ApiResponse<any[]>>(
            `${API_ENDPOINTS.users.profile}/activity`,
            { params }
        );
        return response.data;
    }

    /**
     * Update user timezone
     */
    async updateTimezone(timezone: string): Promise<ApiResponse<User>> {
        const response = await api.patch<ApiResponse<User>>(
            API_ENDPOINTS.users.profile,
            { 
                preferences: { timezone }
            }
        );
        return response.data;
    }

    /**
     * Update user language preference
     */
    async updateLanguage(language: string): Promise<ApiResponse<User>> {
        const response = await api.patch<ApiResponse<User>>(
            API_ENDPOINTS.users.profile,
            { 
                preferences: { language }
            }
        );
        return response.data;
    }

    /**
     * Update user theme preference
     */
    async updateTheme(theme: 'light' | 'dark' | 'system'): Promise<ApiResponse<User>> {
        const response = await api.patch<ApiResponse<User>>(
            API_ENDPOINTS.users.profile,
            { 
                preferences: { theme }
            }
        );
        return response.data;
    }
}

// Export singleton instance
export const userApi = new UserAPI();
