
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ArrowRight, Clock, User } from "lucide-react";

const BlogPreview = () => {
  const blogPosts = [
    {
      title: "10 AI-Powered Resume Tips That Land Interviews",
      excerpt: "Learn how to optimize your resume using AI insights and land 3x more interviews",
      category: "Resume Tips",
      readTime: "5 min read",
      author: "<PERSON>",
      image: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=200&fit=crop"
    },
    {
      title: "The Future of Job Searching: AI vs Traditional Methods",
      excerpt: "Discover why AI-powered job matching is revolutionizing career development",
      category: "Industry Insights",
      readTime: "8 min read",
      author: "<PERSON>",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=200&fit=crop"
    },
    {
      title: "Interview Success: How AI Prep Beats Traditional Coaching",
      excerpt: "Real data on how AI interview preparation outperforms traditional methods",
      category: "Career Growth",
      readTime: "6 min read",
      author: "Maria <PERSON>",
      image: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=400&h=200&fit=crop"
    }
  ];

  return (
    <section className="py-20 bg-white px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4 text-brand-blue-500 border-brand-blue-200 bg-brand-blue-50">
            Latest Insights
          </Badge>
          <h2 className="text-3xl md:text-4xl font-jakarta font-bold text-slate-900 mb-4">
            Career Development <span className="text-transparent bg-gradient-to-r from-brand-blue-500 to-purple-600 bg-clip-text">Resources</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Stay ahead with expert insights, industry trends, and actionable career advice
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {blogPosts.map((post, index) => (
            <Card key={index} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
              <div className="aspect-video overflow-hidden">
                <img 
                  src={post.image} 
                  alt={post.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <CardContent className="p-6">
                <Badge variant="outline" className="mb-3 text-xs">{post.category}</Badge>
                <h3 className="text-lg font-semibold text-slate-900 mb-2 group-hover:text-brand-blue-500 transition-colors">
                  {post.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">{post.excerpt}</p>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <User size={12} />
                      <span>{post.author}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock size={12} />
                      <span>{post.readTime}</span>
                    </div>
                  </div>
                  <ArrowRight size={16} className="text-brand-blue-500 group-hover:translate-x-1 transition-transform" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Button variant="outline" className="border-brand-blue-500 text-brand-blue-500 hover:bg-brand-blue-50">
            View All Articles
            <ArrowRight size={16} className="ml-2" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default BlogPreview;
